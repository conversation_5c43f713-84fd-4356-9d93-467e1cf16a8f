# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

- `npm run build:themes` - Transforms design tokens into TailwindCSS theme configuration files for multiple themes (Neoshare, Volksbank, Sparkasse)
- `npm run build:components` - Transforms design tokens into TailwindCSS component configuration files

## Architecture Overview

This is a design token transformation library that converts Figma design tokens (via Token Studio) into TailwindCSS-compatible configuration files. The library uses StyleDictionary as the core transformation engine.

### Key Components

**Token Processing Pipeline:**
1. Raw design tokens are stored in `tokens.json` (Figma → Token Studio → JSON)
2. Two main build scripts process these tokens:
   - `prepare-tw-themes.js` - Generates theme-specific configuration files
   - `prepare-tw-components.js` - Generates component-specific configuration files

**Token Structure:**
- `1-Base/raw-values` - Base color palette and fundamental values
- `2-Alias` - Semantic token aliases (e.g., primary, secondary colors)
- `3-Component` - Component-specific token overrides
- Themes are built by merging base values with alias and component tokens

**File Organization:**
- `src/style-dictionary/` - Core transformation logic
  - `token-transformers.js` - Custom StyleDictionary transforms (name/kebab, size/pxToRem, font-weight conversion)
  - `tw-themes-formatter.js` - Formats tokens into TailwindCSS theme structure
  - `tw-components-formatter.js` - Formats component tokens with theme() function calls
  - `tokens-filters.js` - Filters for including/excluding specific token types
- `props/` - Generated output files (theme and component configurations)
- `src/utils/` - Utility functions for token filtering and value conversion

**Theme Generation Process:**
1. Filter themes excluding "1-base-raw-values" 
2. Separate alias (2-Alias) and component (3-Component) entries
3. Merge matching component tokens into alias entries
4. Generate individual theme files: `tw-theme-{name}-props.js`

**Component Generation Process:**
1. Uses ts-morph to post-process generated files
2. Converts `{{token.reference}}` syntax to `theme('token.reference')` function calls
3. Outputs single `tw-components-props.js` file

## Token Categories

The system processes these token types:
- **Colors**: Base colors, semantic colors (primary, secondary, etc.)
- **Typography**: Font family, weight, size, line-height
- **Spacing**: Margins, paddings, gaps
- **Border**: Border widths and corner radius
- **Effects**: Box shadows, gradients (ai-gradient)

## Output Structure

Generated files in `props/`:
- `tw-theme-neoshare-props.js`
- `tw-theme-volksbank-props.js` 
- `tw-theme-sparkasse-props.js`
- `tw-components-props.js`
- `index.js` - Main export file combining all themes and components

## Development Notes

- Base font size is set to 10px for rem conversion
- Font weights are multiplied by 10 during transformation
- The library uses ES modules throughout
- Published to private Nexus registry (`@fincloud:registry`)
- Integrates with CI/CD pipeline for automatic builds on token updates