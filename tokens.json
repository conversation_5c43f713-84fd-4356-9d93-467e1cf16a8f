{"1-Base/raw-values": {"base-colors": {"neoshare": {"primary": {"steel-grey": {"10": {"value": "#e9e9ec", "type": "color"}, "20": {"value": "#d4d4d8", "type": "color"}, "30": {"value": "#bebec5", "type": "color"}, "40": {"value": "#a8a9b2", "type": "color"}, "50": {"value": "#92939e", "type": "color"}, "60": {"value": "#7d7e8b", "type": "color"}, "70": {"value": "#676978", "type": "color"}, "80": {"value": "#515365", "type": "color"}, "90": {"value": "#3c3e51", "type": "color"}, "100": {"value": "#26283e", "type": "color"}, "110": {"value": "#1e2032", "type": "color"}}, "ultramarine-blue": {"10": {"value": "#e6e7fa", "type": "color"}, "20": {"value": "#cdcff6", "type": "color"}, "30": {"value": "#b4b7f1", "type": "color"}, "40": {"value": "#9b9fec", "type": "color"}, "50": {"value": "#8287e7", "type": "color"}, "60": {"value": "#6a6fe3", "type": "color"}, "70": {"value": "#5157de", "type": "color"}, "80": {"value": "#383fd9", "type": "color"}, "90": {"value": "#1f27d5", "type": "color"}, "100": {"value": "#060fd0", "type": "color"}, "110": {"value": "#050db7", "type": "color"}}, "lima-green": {"10": {"value": "#f0fcef", "type": "color"}, "20": {"value": "#e1fae0", "type": "color"}, "30": {"value": "#d2f7d0", "type": "color"}, "40": {"value": "#c3f5c0", "type": "color"}, "50": {"value": "#b4f2b0", "type": "color"}, "60": {"value": "#a6efa1", "type": "color"}, "70": {"value": "#97ed91", "type": "color"}, "80": {"value": "#88ea81", "type": "color"}, "90": {"value": "#79e872", "type": "color"}, "100": {"value": "#6ae562", "type": "color"}, "110": {"value": "#5dca56", "type": "color"}}, "fiord-blue": {"10": {"value": "#eceef0", "type": "color"}, "20": {"value": "#d9dce2", "type": "color"}, "30": {"value": "#c6cbd3", "type": "color"}, "40": {"value": "#b3bac5", "type": "color"}, "50": {"value": "#a0a8b6", "type": "color"}, "60": {"value": "#8e97a8", "type": "color"}, "70": {"value": "#7b8699", "type": "color"}, "80": {"value": "#68758b", "type": "color"}, "90": {"value": "#55637d", "type": "color"}, "100": {"value": "#42526e", "type": "color"}, "110": {"value": "#3a4861", "type": "color"}}}, "secondary": {"aqua-green": {"10": {"value": "#e5f0ef", "type": "color"}, "20": {"value": "#cce1df", "type": "color"}, "30": {"value": "#b2d1ce", "type": "color"}, "40": {"value": "#99c2be", "type": "color"}, "50": {"value": "#80b3ae", "type": "color"}, "60": {"value": "#66a49e", "type": "color"}, "70": {"value": "#4d958e", "type": "color"}, "80": {"value": "#33857d", "type": "color"}, "90": {"value": "#1a766d", "type": "color"}, "100": {"value": "#00675d", "type": "color"}, "110": {"value": "#005d54", "type": "color"}}, "sunset-red": {"10": {"value": "#ffefee", "type": "color"}, "20": {"value": "#ffdfdd", "type": "color"}, "30": {"value": "#ffd0cd", "type": "color"}, "40": {"value": "#ffc0bc", "type": "color"}, "50": {"value": "#ffb0ab", "type": "color"}, "60": {"value": "#ffa09a", "type": "color"}, "70": {"value": "#ff9089", "type": "color"}, "80": {"value": "#ff8179", "type": "color"}, "90": {"value": "#ff7168", "type": "color"}, "100": {"value": "#ff6157", "type": "color"}, "110": {"value": "#e5574e", "type": "color"}}, "corn-yellow": {"10": {"value": "#fcfae6", "type": "color"}, "20": {"value": "#f9f4cc", "type": "color"}, "30": {"value": "#f7efb3", "type": "color"}, "40": {"value": "#f4e99a", "type": "color"}, "50": {"value": "#f1e480", "type": "color"}, "60": {"value": "#eedf67", "type": "color"}, "70": {"value": "#ebd94e", "type": "color"}, "80": {"value": "#e9d435", "type": "color"}, "90": {"value": "#e6ce1b", "type": "color"}, "100": {"value": "#e3c902", "type": "color"}, "110": {"value": "#ccb502", "type": "color"}}, "tangerine-orange": {"10": {"value": "#fff5e7", "type": "color"}, "20": {"value": "#ffebce", "type": "color"}, "30": {"value": "#ffe0b6", "type": "color"}, "40": {"value": "#ffd69d", "type": "color"}, "50": {"value": "#ffcc85", "type": "color"}, "60": {"value": "#ffc26d", "type": "color"}, "70": {"value": "#ffb854", "type": "color"}, "80": {"value": "#ffad3c", "type": "color"}, "90": {"value": "#ffa323", "type": "color"}, "100": {"value": "#ff990b", "type": "color"}, "110": {"value": "#e58a0a", "type": "color"}}}, "tertiary": {"twilight-purple": {"value": "#e0ccdc", "type": "color"}, "lace-pink": {"value": "#f5cef8", "type": "color"}, "apple-green": {"value": "#a1e5a3", "type": "color"}, "yellow-green": {"value": "#e1ffaf", "type": "color"}, "french-blue": {"value": "#bedcff", "type": "color"}, "foam-blue": {"value": "#def6fe", "type": "color"}, "orchid-red": {"value": "#d89292", "type": "color"}, "peach-orange": {"value": "#ffd3aa", "type": "color"}}, "opacity": {"grey-70": {"value": "#26283eb3", "type": "color"}, "blue-10": {"value": "#060fd01a", "type": "color"}, "green-20": {"value": "#6ae56233", "type": "color"}, "grey-20": {"value": "#26283e33", "type": "color"}, "blue-20": {"value": "#060fd033", "type": "color"}, "green-10": {"value": "#6ae5621a", "type": "color"}, "red-20": {"value": "#ff615733", "type": "color"}, "red-10": {"value": "#ff61571a", "type": "color"}, "grey-15": {"value": "#26283e26", "type": "color"}, "neutral-40": {"value": "#f8f8f866", "type": "color"}}, "neutrals": {"white": {"value": "#ffffff", "type": "color"}, "grey-10": {"value": "#f8f8f8", "type": "color"}, "grey-20": {"value": "#ececec", "type": "color"}, "grey-15": {"value": "#f3f3f3", "type": "color"}}}, "volksbank": {"neutrals": {"white": {"value": "#ffffff", "type": "color"}, "grey-10": {"value": "#f8f8f8", "type": "color"}, "grey-15": {"value": "#f3f3f3", "type": "color"}, "grey-20": {"value": "#ececec", "type": "color"}}, "primary": {"midnight-blue": {"10": {"value": "#e5eaf0", "type": "color"}, "20": {"value": "#ccd5e1", "type": "color"}, "30": {"value": "#b2c0d1", "type": "color"}, "40": {"value": "#99abc2", "type": "color"}, "50": {"value": "#8096b3", "type": "color"}, "60": {"value": "#6681a4", "type": "color"}, "70": {"value": "#4d6c95", "type": "color"}, "80": {"value": "#335785", "type": "color"}, "90": {"value": "#1a4276", "type": "color"}, "100": {"value": "#002d67", "type": "color"}, "110": {"value": "#002658", "type": "color"}}, "endeavour-blue": {"10": {"value": "#e5f0f7", "type": "color"}, "20": {"value": "#cce0f0", "type": "color"}, "30": {"value": "#b2d1e8", "type": "color"}, "40": {"value": "#99c2e1", "type": "color"}, "50": {"value": "#80b2d9", "type": "color"}, "60": {"value": "#66a3d1", "type": "color"}, "70": {"value": "#4d94ca", "type": "color"}, "80": {"value": "#3385c2", "type": "color"}, "90": {"value": "#1a75bb", "type": "color"}, "100": {"value": "#0066b3", "type": "color"}, "110": {"value": "#005798", "type": "color"}}, "blaze-orange": {"10": {"value": "#ffefe5", "type": "color"}, "20": {"value": "#ffe0cc", "type": "color"}, "30": {"value": "#ffd0b2", "type": "color"}, "40": {"value": "#ffc099", "type": "color"}, "50": {"value": "#ffb080", "type": "color"}, "60": {"value": "#ffa166", "type": "color"}, "70": {"value": "#ff914d", "type": "color"}, "80": {"value": "#ff8133", "type": "color"}, "90": {"value": "#ff721a", "type": "color"}, "100": {"value": "#ff6200", "type": "color"}, "110": {"value": "#d95300", "type": "color"}}, "woodsmoke-grey": {"10": {"value": "#f4f4f6", "type": "color"}, "20": {"value": "#e8eaec", "type": "color"}, "30": {"value": "#dddfe3", "type": "color"}, "40": {"value": "#d1d5da", "type": "color"}, "50": {"value": "#c6cad1", "type": "color"}, "60": {"value": "#bbbfc7", "type": "color"}, "70": {"value": "#afb5be", "type": "color"}, "80": {"value": "#a4aab5", "type": "color"}, "90": {"value": "#98a0ab", "type": "color"}, "100": {"value": "#8d95a2", "type": "color"}, "110": {"value": "#7f8692", "type": "color"}}}, "secondary": {"mint-green": {"10": {"value": "#e5f0f1", "type": "color"}, "20": {"value": "#cce2e3", "type": "color"}, "30": {"value": "#b2d3d5", "type": "color"}, "40": {"value": "#99c5c7", "type": "color"}, "50": {"value": "#80b6b9", "type": "color"}, "60": {"value": "#66a8ab", "type": "color"}, "70": {"value": "#4d999d", "type": "color"}, "80": {"value": "#338b8f", "type": "color"}, "90": {"value": "#1a7d81", "type": "color"}, "100": {"value": "#006e73", "type": "color"}, "110": {"value": "#005e62", "type": "color"}}, "watermelon-red": {"10": {"value": "#ffeff1", "type": "color"}, "20": {"value": "#fedfe3", "type": "color"}, "30": {"value": "#fed0d5", "type": "color"}, "40": {"value": "#fec0c7", "type": "color"}, "50": {"value": "#fdb0b9", "type": "color"}, "60": {"value": "#fda0ac", "type": "color"}, "70": {"value": "#fd909e", "type": "color"}, "80": {"value": "#fd8190", "type": "color"}, "90": {"value": "#fc7182", "type": "color"}, "100": {"value": "#fc6174", "type": "color"}, "110": {"value": "#e35768", "type": "color"}}, "amber-yellow": {"10": {"value": "#fef8ec", "type": "color"}, "20": {"value": "#fcf2d8", "type": "color"}, "30": {"value": "#fbebc5", "type": "color"}, "40": {"value": "#f9e5b2", "type": "color"}, "50": {"value": "#f8df9e", "type": "color"}, "60": {"value": "#f7d88b", "type": "color"}, "70": {"value": "#f5d278", "type": "color"}, "80": {"value": "#f4cb65", "type": "color"}, "90": {"value": "#f2c451", "type": "color"}, "100": {"value": "#f1be3e", "type": "color"}, "110": {"value": "#d9ab38", "type": "color"}}, "apricot-orange": {"10": {"value": "#fff6f0", "type": "color"}, "20": {"value": "#ffece0", "type": "color"}, "30": {"value": "#ffe3d1", "type": "color"}, "40": {"value": "#ffd9c2", "type": "color"}, "50": {"value": "#ffd0b2", "type": "color"}, "60": {"value": "#fec7a3", "type": "color"}, "70": {"value": "#febd94", "type": "color"}, "80": {"value": "#feb485", "type": "color"}, "90": {"value": "#feaa75", "type": "color"}, "100": {"value": "#fea166", "type": "color"}, "110": {"value": "#e5915c", "type": "color"}}}, "tertiary": {"viola-pink": {"value": "#cd99ae", "type": "color"}, "prim-pink": {"value": "#f0dde6", "type": "color"}, "lavender-purple": {"value": "#9a7ab0", "type": "color"}, "water-blue": {"value": "#e1e6ed", "type": "color"}, "sisal-brown": {"value": "#d4c8bf", "type": "color"}, "malibu-blue": {"value": "#7cb7f1", "type": "color"}, "london-purple": {"value": "#c8b4d4", "type": "color"}, "avocado-green": {"value": "#a69962", "type": "color"}}, "opacity": {"orange-20": {"value": "#ff620033", "type": "color"}, "blue-10": {"value": "#0066b31a", "type": "color"}, "midnight-70": {"value": "#002d67b3", "type": "color"}, "orange-10": {"value": "#ff62001a", "type": "color"}, "red-20": {"value": "#fc617433", "type": "color"}, "blue-20": {"value": "#0066b333", "type": "color"}, "red-10": {"value": "#fc61741a", "type": "color"}, "midnight-20": {"value": "#002d6733", "type": "color"}, "midnight-15": {"value": "#002d6726", "type": "color"}, "neutral-40": {"value": "#f8f8f866", "type": "color"}}}, "sparkasse": {"neutrals": {"white": {"value": "#ffffff", "type": "color"}, "grey-10": {"value": "#f8f8f8", "type": "color"}, "grey-15": {"value": "#f3f3f3", "type": "color"}, "grey-20": {"value": "#ececec", "type": "color"}}, "primary": {"plain-black": {"10": {"value": "#e5e5e5", "type": "color"}, "20": {"value": "#cccccc", "type": "color"}, "30": {"value": "#b2b2b2", "type": "color"}, "40": {"value": "#999999", "type": "color"}, "50": {"value": "#808080", "type": "color"}, "60": {"value": "#666666", "type": "color"}, "70": {"value": "#4d4d4d", "type": "color"}, "80": {"value": "#333333", "type": "color"}, "90": {"value": "#1a1a1a", "type": "color"}, "100": {"value": "#000000", "type": "color"}, "110": {"value": "#000000", "type": "color"}}, "tundora-grey": {"10": {"value": "#ececec", "type": "color"}, "20": {"value": "#dadada", "type": "color"}, "30": {"value": "#c7c7c7", "type": "color"}, "40": {"value": "#b4b4b4", "type": "color"}, "50": {"value": "#a1a1a1", "type": "color"}, "60": {"value": "#8f8f8f", "type": "color"}, "70": {"value": "#7c7c7c", "type": "color"}, "80": {"value": "#696969", "type": "color"}, "90": {"value": "#575757", "type": "color"}, "100": {"value": "#444444", "type": "color"}, "110": {"value": "#3d3d3d", "type": "color"}}, "scarlet-red": {"10": {"value": "#fde5e5", "type": "color"}, "20": {"value": "#fccccc", "type": "color"}, "30": {"value": "#fab2b2", "type": "color"}, "40": {"value": "#f89999", "type": "color"}, "50": {"value": "#f78080", "type": "color"}, "60": {"value": "#f56666", "type": "color"}, "70": {"value": "#f34d4d", "type": "color"}, "80": {"value": "#f13333", "type": "color"}, "90": {"value": "#f01a1a", "type": "color"}, "100": {"value": "#ee0000", "type": "color"}, "110": {"value": "#d60000", "type": "color"}}, "dove-grey": {"10": {"value": "#f0f0f0", "type": "color"}, "20": {"value": "#e0e0e0", "type": "color"}, "30": {"value": "#d1d1d1", "type": "color"}, "40": {"value": "#c2c2c2", "type": "color"}, "50": {"value": "#b2b2b2", "type": "color"}, "60": {"value": "#a3a3a3", "type": "color"}, "70": {"value": "#949494", "type": "color"}, "80": {"value": "#858585", "type": "color"}, "90": {"value": "#757575", "type": "color"}, "100": {"value": "#666666", "type": "color"}, "110": {"value": "#616161", "type": "color"}}}, "secondary": {"leaf-green": {"10": {"value": "#eef5e1", "type": "color"}, "20": {"value": "#deebc4", "type": "color"}, "30": {"value": "#cddea9", "type": "color"}, "40": {"value": "#bacd93", "type": "color"}, "50": {"value": "#a8bd7b", "type": "color"}, "60": {"value": "#95ac64", "type": "color"}, "70": {"value": "#829c4d", "type": "color"}, "80": {"value": "#718e33", "type": "color"}, "90": {"value": "#5f801a", "type": "color"}, "100": {"value": "#4d7200", "type": "color"}, "110": {"value": "#456700", "type": "color"}}, "milano-red": {"10": {"value": "#fce1e1", "type": "color"}, "20": {"value": "#f5c9c9", "type": "color"}, "30": {"value": "#efaeae", "type": "color"}, "40": {"value": "#e99595", "type": "color"}, "50": {"value": "#e37c7c", "type": "color"}, "60": {"value": "#db6363", "type": "color"}, "70": {"value": "#d24d4d", "type": "color"}, "80": {"value": "#cb3333", "type": "color"}, "90": {"value": "#be0000", "type": "color"}, "100": {"value": "#be0000", "type": "color"}, "110": {"value": "#ab0000", "type": "color"}}, "supernova-yellow": {"10": {"value": "#fdf9e4", "type": "color"}, "20": {"value": "#fbf0cb", "type": "color"}, "30": {"value": "#f7e9b2", "type": "color"}, "40": {"value": "#f5e199", "type": "color"}, "50": {"value": "#f2da80", "type": "color"}, "60": {"value": "#efd366", "type": "color"}, "70": {"value": "#eac433", "type": "color"}, "80": {"value": "#eac433", "type": "color"}, "90": {"value": "#e8bc1a", "type": "color"}, "100": {"value": "#e5b500", "type": "color"}, "110": {"value": "#cea300", "type": "color"}}, "pizazz-orange": {"10": {"value": "#fef2e3", "type": "color"}, "20": {"value": "#fce6ca", "type": "color"}, "30": {"value": "#f9d9b0", "type": "color"}, "40": {"value": "#f6cd98", "type": "color"}, "50": {"value": "#f2c080", "type": "color"}, "60": {"value": "#efb366", "type": "color"}, "70": {"value": "#eda74d", "type": "color"}, "80": {"value": "#ea9a33", "type": "color"}, "90": {"value": "#e88e1a", "type": "color"}, "100": {"value": "#e58100", "type": "color"}, "110": {"value": "#ce7400", "type": "color"}}}, "tertiary": {"blizzard-blue": {"value": "#a5e1ef", "type": "color"}, "white-blue": {"value": "#e8f7fb", "type": "color"}, "conifer-green": {"value": "#c7e882", "type": "color"}, "tusk-green": {"value": "#edf7d6", "type": "color"}, "derby-orange": {"value": "#ffedd6", "type": "color"}, "chardonnay": {"value": "#ffc882", "type": "color"}, "lilac-purple": {"value": "#ce9cc8", "type": "color"}, "foam-pink": {"value": "#efdfed", "type": "color"}}, "opacity": {"neutral-40": {"value": "#f8f8f866", "type": "color"}, "black-70": {"value": "#000000b3", "type": "color"}, "black-20": {"value": "#00000033", "type": "color"}, "black-15": {"value": "#00000026", "type": "color"}, "tundora-20": {"value": "#44444433", "type": "color"}, "tundora-10": {"value": "#4444441a", "type": "color"}, "green-20": {"value": "#4d720033", "type": "color"}, "green-10": {"value": "#4d72001a", "type": "color"}, "red-20": {"value": "#ee000033", "type": "color"}, "red-10": {"value": "#ee00001a", "type": "color"}}}}, "base-typography": {"neoshare": {"font-family": {"chopin": {"value": "<PERSON><PERSON>", "type": "text"}}, "size": {"d1-48": {"value": "48px", "type": "dimension"}, "h1-30": {"value": "30px", "type": "dimension"}, "h2-24": {"value": "24px", "type": "dimension"}, "h3-20": {"value": "20px", "type": "dimension"}, "b1-16": {"value": "16px", "type": "dimension"}, "b2-14": {"value": "14px", "type": "dimension"}, "b3-12": {"value": "12px", "type": "dimension"}, "c1-10": {"value": "10px", "type": "dimension"}}, "weight": {"thin-100": {"value": "100px", "type": "dimension"}, "ultralight-200": {"value": "200px", "type": "dimension"}, "light-300": {"value": "300px", "type": "dimension"}, "regular-400": {"value": "400px", "type": "dimension"}, "medium-500": {"value": "500px", "type": "dimension"}, "semibold-600": {"value": "600px", "type": "dimension"}, "bold-700": {"value": "700px", "type": "dimension"}, "extrabold-800": {"value": "800px", "type": "dimension"}, "black-900": {"value": "900px", "type": "dimension"}}}, "volksbank": {"font-family": {"asterisk-sans-pro": {"value": "Asterisk Sans Pro", "type": "text"}}, "weight": {"thin-100": {"value": "100px", "type": "dimension"}, "ultralight-200": {"value": "200px", "type": "dimension"}, "light-300": {"value": "300px", "type": "dimension"}, "regular-400": {"value": "400px", "type": "dimension"}, "medium-500": {"value": "500px", "type": "dimension"}, "semibold-600": {"value": "600px", "type": "dimension"}, "bold-700": {"value": "700px", "type": "dimension"}, "extrabold-800": {"value": "800px", "type": "dimension"}, "black-900": {"value": "900px", "type": "dimension"}}, "size": {"d1-48": {"value": "48px", "type": "dimension"}, "h1-30": {"value": "30px", "type": "dimension"}, "h2-24": {"value": "24px", "type": "dimension"}, "h3-20": {"value": "20px", "type": "dimension"}, "b1-16": {"value": "16px", "type": "dimension"}, "b2-14": {"value": "14px", "type": "dimension"}, "b3-12": {"value": "12px", "type": "dimension"}, "c1-10": {"value": "10px", "type": "dimension"}}}, "sparkasse": {"font-family": {"sparkasse-rg": {"value": "Sparkasse Rg", "type": "text"}}, "weight": {"thin-100": {"value": "100px", "type": "dimension"}, "ultralight-200": {"value": "200px", "type": "dimension"}, "light-300": {"value": "300px", "type": "dimension"}, "regular-400": {"value": "400px", "type": "dimension"}, "medium-500": {"value": "500px", "type": "dimension"}, "semibold-600": {"value": "600px", "type": "dimension"}, "bold-700": {"value": "700px", "type": "dimension"}, "extrabold-800": {"value": "800px", "type": "dimension"}, "black-900": {"value": "900px", "type": "dimension"}}, "size": {"d1-48": {"value": "48px", "type": "dimension"}, "h1-30": {"value": "30px", "type": "dimension"}, "h2-24": {"value": "24px", "type": "dimension"}, "h3-20": {"value": "20px", "type": "dimension"}, "b1-16": {"value": "16px", "type": "dimension"}, "b2-14": {"value": "14px", "type": "dimension"}, "b3-12": {"value": "12px", "type": "dimension"}, "c1-10": {"value": "10px", "type": "dimension"}}}}, "base-numbers": {"base-none": {"value": "0px", "type": "dimension"}, "base-2": {"value": "2px", "type": "dimension"}, "base-4": {"value": "4px", "type": "dimension"}, "base-6": {"value": "6px", "type": "dimension"}, "base-8": {"value": "8px", "type": "dimension"}, "base-10": {"value": "10px", "type": "dimension"}, "base-12": {"value": "12px", "type": "dimension"}, "base-14": {"value": "14px", "type": "dimension"}, "base-16": {"value": "16px", "type": "dimension"}, "base-20": {"value": "20px", "type": "dimension"}, "base-24": {"value": "24px", "type": "dimension"}, "base-28": {"value": "28px", "type": "dimension"}, "base-32": {"value": "32px", "type": "dimension"}, "base-40": {"value": "40px", "type": "dimension"}, "base-44": {"value": "44px", "type": "dimension"}, "base-48": {"value": "48px", "type": "dimension"}, "base-56": {"value": "56px", "type": "dimension"}, "base-64": {"value": "64px", "type": "dimension"}, "base-80": {"value": "80px", "type": "dimension"}, "base-96": {"value": "96px", "type": "dimension"}, "base-112": {"value": "112px", "type": "dimension"}, "base-128": {"value": "128px", "type": "dimension"}, "base-144": {"value": "144px", "type": "dimension"}, "base-160": {"value": "160px", "type": "dimension"}, "base-72": {"value": "72px", "type": "dimension"}, "base-9999": {"value": "9999px", "type": "dimension"}, "base-30": {"value": "30px", "type": "dimension"}, "base-1": {"value": "1px", "type": "dimension"}, "base-120": {"value": "120px", "type": "dimension"}, "base-104": {"value": "104px", "type": "dimension"}, "base-18": {"value": "18px", "type": "dimension"}, "base-36": {"value": "36px", "type": "dimension"}}}, "2-Alias/neoshare": {"color": {"brand": {"primary": {"value": "{base-colors.neoshare.primary.lima-green.100}", "type": "color"}, "dark": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "tertiary": {"value": "{base-colors.neoshare.primary.fiord-blue.100}", "type": "color"}, "secondary": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}}, "surface": {"primary": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "secondary": {"value": "{base-colors.neoshare.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.neutrals.grey-20}", "type": "color"}, "dark-strong": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "tertiary": {"value": "{base-colors.neoshare.neutrals.grey-15}", "type": "color"}, "dark-subtle": {"value": "{base-colors.neoshare.primary.fiord-blue.100}", "type": "color"}}, "status": {"success": {"value": "{base-colors.neoshare.secondary.aqua-green.100}", "type": "color"}, "error": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "failure": {"value": "{base-colors.neoshare.secondary.corn-yellow.100}", "type": "color"}, "warning": {"value": "{base-colors.neoshare.secondary.tangerine-orange.100}", "type": "color"}, "neutral": {"value": "{base-colors.neoshare.primary.fiord-blue.100}", "type": "color"}, "informative": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}}, "advanced-editor": {"first": {"value": "{base-colors.neoshare.tertiary.twilight-purple}", "type": "color"}, "second": {"value": "{base-colors.neoshare.tertiary.lace-pink}", "type": "color"}, "third": {"value": "{base-colors.neoshare.tertiary.french-blue}", "type": "color"}, "fourth": {"value": "{base-colors.neoshare.tertiary.foam-blue}", "type": "color"}, "fifth": {"value": "{base-colors.neoshare.tertiary.apple-green}", "type": "color"}, "sixth": {"value": "{base-colors.neoshare.tertiary.yellow-green}", "type": "color"}, "seventh": {"value": "{base-colors.neoshare.tertiary.orchid-red}", "type": "color"}, "eighth": {"value": "{base-colors.neoshare.tertiary.peach-orange}", "type": "color"}}, "text": {"tertiary": {"value": "{base-colors.neoshare.primary.steel-grey.60}", "type": "color"}, "primary": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "interactive": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "light": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "secondary": {"value": "{base-colors.neoshare.primary.fiord-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "success": {"value": "{base-colors.neoshare.secondary.aqua-green.100}", "type": "color"}, "error": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "warning": {"value": "{base-colors.neoshare.secondary.tangerine-orange.100}", "type": "color"}, "minimal": {"value": "{base-colors.neoshare.primary.steel-grey.20}", "type": "color"}}, "border": {"default": {"primary": {"value": "{base-colors.neoshare.primary.steel-grey.20}", "type": "color"}, "inactive": {"value": "{base-colors.neoshare.primary.steel-grey.30}", "type": "color"}, "success": {"value": "{base-colors.neoshare.secondary.aqua-green.100}", "type": "color"}, "error": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "warning": {"value": "{base-colors.neoshare.secondary.tangerine-orange.100}", "type": "color"}, "minimal": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "interactive": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "light": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "strong": {"value": "{base-colors.neoshare.primary.steel-grey.50}", "type": "color"}}, "company-graph": {"ubo": {"value": "{base-colors.neoshare.primary.ultramarine-blue.50}", "type": "color"}, "company": {"value": "{base-colors.neoshare.secondary.aqua-green.50}", "type": "color"}, "person": {"value": "{base-colors.neoshare.secondary.tangerine-orange.50}", "type": "color"}}}, "icons": {"primary": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "success": {"value": "{base-colors.neoshare.secondary.aqua-green.100}", "type": "color"}, "error": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "brand-primary": {"value": "{base-colors.neoshare.primary.lima-green.100}", "type": "color"}, "interactive": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "light": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "tertiary": {"value": "{base-colors.neoshare.primary.steel-grey.50}", "type": "color"}, "dark": {"value": "{base-colors.neoshare.primary.steel-grey.70}", "type": "color"}, "minimal": {"value": "{base-colors.neoshare.primary.steel-grey.30}", "type": "color"}, "secondary": {"value": "{base-colors.neoshare.primary.fiord-blue.100}", "type": "color"}, "warning": {"value": "{base-colors.neoshare.secondary.tangerine-orange.100}", "type": "color"}}, "background": {"primary-strong": {"value": "{base-colors.neoshare.primary.lima-green.100}", "type": "color"}, "secondary-strong": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "tertiary-subtle": {"value": "{base-colors.neoshare.primary.fiord-blue.20}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "dark-strong": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "primary-moderate": {"value": "{base-colors.neoshare.primary.lima-green.50}", "type": "color"}, "secondary-subtle": {"value": "{base-colors.neoshare.primary.ultramarine-blue.20}", "type": "color"}, "dark-subtle": {"value": "{base-colors.neoshare.primary.steel-grey.20}", "type": "color"}, "warning-strong": {"value": "{base-colors.neoshare.secondary.tangerine-orange.100}", "type": "color"}, "warning-subtle": {"value": "{base-colors.neoshare.secondary.tangerine-orange.20}", "type": "color"}, "success-strong": {"value": "{base-colors.neoshare.secondary.aqua-green.100}", "type": "color"}, "success-subtle": {"value": "{base-colors.neoshare.secondary.aqua-green.20}", "type": "color"}, "failure-strong": {"value": "{base-colors.neoshare.secondary.corn-yellow.100}", "type": "color"}, "failure-subtle": {"value": "{base-colors.neoshare.secondary.corn-yellow.20}", "type": "color"}, "attention-strong": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-subtle": {"value": "{base-colors.neoshare.secondary.sunset-red.20}", "type": "color"}, "neutral-minimal": {"value": "{base-colors.neoshare.neutrals.grey-10}", "type": "color"}, "light": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "tertiary-strong": {"value": "{base-colors.neoshare.primary.fiord-blue.100}", "type": "color"}, "dark-moderate": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "dark-bold": {"value": "{base-colors.neoshare.primary.steel-grey.70}", "type": "color"}, "primary-minimal": {"value": "{base-colors.neoshare.primary.lima-green.10}", "type": "color"}, "secondary-minimal": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}, "tertiary-minimal": {"value": "{base-colors.neoshare.primary.fiord-blue.10}", "type": "color"}, "attention-minimal": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}, "warning-minimal": {"value": "{base-colors.neoshare.secondary.tangerine-orange.10}", "type": "color"}, "success-minimal": {"value": "{base-colors.neoshare.secondary.aqua-green.10}", "type": "color"}, "failure-minimal": {"value": "{base-colors.neoshare.secondary.corn-yellow.10}", "type": "color"}, "primary-subtle": {"value": "{base-colors.neoshare.primary.lima-green.20}", "type": "color"}, "secondary-moderate": {"value": "{base-colors.neoshare.primary.ultramarine-blue.50}", "type": "color"}, "tertiary-moderate": {"value": "{base-colors.neoshare.primary.fiord-blue.50}", "type": "color"}, "attention-moderate": {"value": "{base-colors.neoshare.secondary.sunset-red.50}", "type": "color"}, "warning-moderate": {"value": "{base-colors.neoshare.secondary.tangerine-orange.50}", "type": "color"}, "success-bold": {"value": "{base-colors.neoshare.secondary.aqua-green.80}", "type": "color"}, "failure-moderate": {"value": "{base-colors.neoshare.secondary.corn-yellow.50}", "type": "color"}, "primary-bold": {"value": "{base-colors.neoshare.primary.lima-green.80}", "type": "color"}, "secondary-bold": {"value": "{base-colors.neoshare.primary.ultramarine-blue.80}", "type": "color"}, "tertiary-bold": {"value": "{base-colors.neoshare.primary.fiord-blue.80}", "type": "color"}, "attention-bold": {"value": "{base-colors.neoshare.secondary.sunset-red.80}", "type": "color"}, "warning-bold": {"value": "{base-colors.neoshare.secondary.tangerine-orange.80}", "type": "color"}, "success-moderate": {"value": "{base-colors.neoshare.secondary.aqua-green.50}", "type": "color"}, "failure-bold": {"value": "{base-colors.neoshare.secondary.corn-yellow.80}", "type": "color"}, "dark-minimal": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}}, "hover": {"primary": {"value": "{base-colors.neoshare.primary.lima-green.110}", "type": "color"}, "tertiary": {"value": "{base-colors.neoshare.primary.fiord-blue.20}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.110}", "type": "color"}, "secondary": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "neutral": {"value": "{base-colors.neoshare.neutrals.grey-20}", "type": "color"}, "informative": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}}, "transparency": {"attention-subtle": {"value": "{base-colors.neoshare.opacity.red-20}", "type": "color"}, "attention-minimal": {"value": "{base-colors.neoshare.opacity.red-10}", "type": "color"}, "secondary-subtle": {"value": "{base-colors.neoshare.opacity.blue-20}", "type": "color"}, "secondary-minimal": {"value": "{base-colors.neoshare.opacity.blue-10}", "type": "color"}, "dark-subtle": {"value": "{base-colors.neoshare.opacity.grey-20}", "type": "color"}, "dark-bold": {"value": "{base-colors.neoshare.opacity.grey-70}", "type": "color"}, "primary-minimal": {"value": "{base-colors.neoshare.opacity.green-10}", "type": "color"}, "primary-subtle": {"value": "{base-colors.neoshare.opacity.green-20}", "type": "color"}, "neutral-moderate": {"value": "{base-colors.neoshare.opacity.neutral-40}", "type": "color"}}, "elevation": {"small": {"value": "{base-colors.neoshare.opacity.grey-15}", "type": "color"}, "medium": {"value": "{base-colors.neoshare.opacity.grey-15}", "type": "color"}, "large": {"value": "{base-colors.neoshare.opacity.grey-20}", "type": "color"}}}, "text": {"display-1": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.d1-48}", "type": "dimension"}, "strong-weight": {"value": "{base-typography.neoshare.weight.semibold-600}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-72}", "type": "dimension"}}, "heading-2": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "strong-weight": {"value": "{base-typography.neoshare.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h2-24}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-36}", "type": "dimension"}}, "heading-1": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "strong-weight": {"value": "{base-typography.neoshare.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h1-30}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-48}", "type": "dimension"}}, "heading-3": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "strong-weight": {"value": "{base-typography.neoshare.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h3-20}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-30}", "type": "dimension"}}, "body-1": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "strong-weight": {"value": "{base-typography.neoshare.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b1-16}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-24}", "type": "dimension"}}, "body-3": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "strong-weight": {"value": "{base-typography.neoshare.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b3-12}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-18}", "type": "dimension"}}, "body-2": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "strong-weight": {"value": "{base-typography.neoshare.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b2-14}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-20}", "type": "dimension"}}, "caption-1": {"family": {"value": "{base-typography.neoshare.font-family.chopin}", "type": "text"}, "size": {"value": "{base-typography.neoshare.size.c1-10}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.neoshare.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-16}", "type": "dimension"}}}, "size": {"spacing": {"1": {"value": "{base-numbers.base-1}", "type": "dimension"}, "2": {"value": "{base-numbers.base-2}", "type": "dimension"}, "4": {"value": "{base-numbers.base-4}", "type": "dimension"}, "6": {"value": "{base-numbers.base-6}", "type": "dimension"}, "8": {"value": "{base-numbers.base-8}", "type": "dimension"}, "12": {"value": "{base-numbers.base-12}", "type": "dimension"}, "16": {"value": "{base-numbers.base-16}", "type": "dimension"}, "18": {"value": "{base-numbers.base-18}", "type": "dimension"}, "20": {"value": "{base-numbers.base-20}", "type": "dimension"}, "24": {"value": "{base-numbers.base-24}", "type": "dimension"}, "32": {"value": "{base-numbers.base-32}", "type": "dimension"}, "40": {"value": "{base-numbers.base-40}", "type": "dimension"}, "48": {"value": "{base-numbers.base-48}", "type": "dimension"}, "56": {"value": "{base-numbers.base-56}", "type": "dimension"}, "64": {"value": "{base-numbers.base-64}", "type": "dimension"}, "80": {"value": "{base-numbers.base-80}", "type": "dimension"}, "120": {"value": "{base-numbers.base-120}", "type": "dimension"}, "none": {"value": "{base-numbers.base-none}", "type": "dimension"}}, "border": {"none": {"value": "{base-numbers.base-none}", "type": "dimension"}, "s": {"value": "{base-numbers.base-1}", "type": "dimension"}, "m": {"value": "{base-numbers.base-2}", "type": "dimension"}}, "corner-radius": {"square": {"value": "{base-numbers.base-none}", "type": "dimension"}, "s": {"value": "{base-numbers.base-4}", "type": "dimension"}, "m": {"value": "{base-numbers.base-8}", "type": "dimension"}, "l": {"value": "{base-numbers.base-12}", "type": "dimension"}, "round": {"value": "{base-numbers.base-9999}", "type": "dimension"}}}}, "2-Alias/volksbank": {"color": {"brand": {"primary": {"value": "{base-colors.volksbank.primary.blaze-orange.100}", "type": "color"}, "dark": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "tertiary": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.100}", "type": "color"}, "secondary": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}}, "surface": {"primary": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "secondary": {"value": "{base-colors.volksbank.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.grey-20}", "type": "color"}, "dark-strong": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "tertiary": {"value": "{base-colors.volksbank.neutrals.grey-15}", "type": "color"}, "dark-subtle": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.100}", "type": "color"}}, "status": {"success": {"value": "{base-colors.volksbank.secondary.mint-green.100}", "type": "color"}, "error": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "failure": {"value": "{base-colors.volksbank.secondary.amber-yellow.100}", "type": "color"}, "warning": {"value": "{base-colors.volksbank.secondary.apricot-orange.100}", "type": "color"}, "neutral": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.100}", "type": "color"}, "informative": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}}, "advanced-editor": {"first": {"value": "{base-colors.volksbank.tertiary.viola-pink}", "type": "color"}, "second": {"value": "{base-colors.volksbank.tertiary.prim-pink}", "type": "color"}, "third": {"value": "{base-colors.volksbank.tertiary.malibu-blue}", "type": "color"}, "fourth": {"value": "{base-colors.volksbank.tertiary.water-blue}", "type": "color"}, "fifth": {"value": "{base-colors.volksbank.tertiary.avocado-green}", "type": "color"}, "sixth": {"value": "{base-colors.volksbank.tertiary.sisal-brown}", "type": "color"}, "seventh": {"value": "{base-colors.volksbank.tertiary.lavender-purple}", "type": "color"}, "eighth": {"value": "{base-colors.volksbank.tertiary.london-purple}", "type": "color"}}, "text": {"tertiary": {"value": "{base-colors.volksbank.primary.midnight-blue.60}", "type": "color"}, "primary": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "interactive": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "light": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "secondary": {"value": "{base-colors.volksbank.primary.midnight-blue.80}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "success": {"value": "{base-colors.volksbank.secondary.mint-green.100}", "type": "color"}, "error": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "warning": {"value": "{base-colors.volksbank.secondary.apricot-orange.100}", "type": "color"}, "minimal": {"value": "{base-colors.volksbank.primary.midnight-blue.20}", "type": "color"}}, "border": {"default": {"primary": {"value": "{base-colors.volksbank.primary.midnight-blue.20}", "type": "color"}, "inactive": {"value": "{base-colors.volksbank.primary.midnight-blue.30}", "type": "color"}, "success": {"value": "{base-colors.volksbank.secondary.mint-green.100}", "type": "color"}, "error": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "warning": {"value": "{base-colors.volksbank.secondary.apricot-orange.100}", "type": "color"}, "minimal": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "interactive": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "light": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "strong": {"value": "{base-colors.volksbank.primary.midnight-blue.50}", "type": "color"}}, "company-graph": {"ubo": {"value": "{base-colors.volksbank.primary.endeavour-blue.50}", "type": "color"}, "company": {"value": "{base-colors.volksbank.secondary.mint-green.50}", "type": "color"}, "person": {"value": "{base-colors.volksbank.secondary.apricot-orange.50}", "type": "color"}}}, "icons": {"primary": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "success": {"value": "{base-colors.volksbank.secondary.mint-green.100}", "type": "color"}, "error": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "brand-primary": {"value": "{base-colors.volksbank.primary.blaze-orange.100}", "type": "color"}, "interactive": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "light": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "tertiary": {"value": "{base-colors.volksbank.primary.midnight-blue.50}", "type": "color"}, "dark": {"value": "{base-colors.volksbank.primary.midnight-blue.70}", "type": "color"}, "minimal": {"value": "{base-colors.volksbank.primary.midnight-blue.30}", "type": "color"}, "secondary": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.100}", "type": "color"}, "warning": {"value": "{base-colors.volksbank.secondary.apricot-orange.100}", "type": "color"}}, "background": {"primary-strong": {"value": "{base-colors.volksbank.primary.blaze-orange.100}", "type": "color"}, "secondary-strong": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "tertiary-subtle": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.20}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "dark-strong": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "primary-moderate": {"value": "{base-colors.volksbank.primary.blaze-orange.50}", "type": "color"}, "secondary-subtle": {"value": "{base-colors.volksbank.primary.endeavour-blue.20}", "type": "color"}, "dark-subtle": {"value": "{base-colors.volksbank.primary.midnight-blue.20}", "type": "color"}, "warning-strong": {"value": "{base-colors.volksbank.secondary.apricot-orange.100}", "type": "color"}, "warning-subtle": {"value": "{base-colors.volksbank.secondary.apricot-orange.20}", "type": "color"}, "success-strong": {"value": "{base-colors.volksbank.secondary.mint-green.100}", "type": "color"}, "success-subtle": {"value": "{base-colors.volksbank.secondary.mint-green.20}", "type": "color"}, "failure-strong": {"value": "{base-colors.volksbank.secondary.amber-yellow.100}", "type": "color"}, "failure-subtle": {"value": "{base-colors.volksbank.secondary.amber-yellow.20}", "type": "color"}, "attention-strong": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-subtle": {"value": "{base-colors.volksbank.secondary.watermelon-red.20}", "type": "color"}, "neutral-minimal": {"value": "{base-colors.volksbank.neutrals.grey-10}", "type": "color"}, "light": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "tertiary-strong": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.100}", "type": "color"}, "dark-moderate": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "dark-bold": {"value": "{base-colors.volksbank.primary.midnight-blue.70}", "type": "color"}, "primary-minimal": {"value": "{base-colors.volksbank.primary.blaze-orange.10}", "type": "color"}, "secondary-minimal": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}, "tertiary-minimal": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.10}", "type": "color"}, "attention-minimal": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}, "warning-minimal": {"value": "{base-colors.volksbank.secondary.apricot-orange.10}", "type": "color"}, "success-minimal": {"value": "{base-colors.volksbank.secondary.mint-green.10}", "type": "color"}, "failure-minimal": {"value": "{base-colors.volksbank.secondary.amber-yellow.10}", "type": "color"}, "primary-subtle": {"value": "{base-colors.volksbank.primary.blaze-orange.20}", "type": "color"}, "secondary-moderate": {"value": "{base-colors.volksbank.primary.endeavour-blue.50}", "type": "color"}, "tertiary-moderate": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.50}", "type": "color"}, "attention-moderate": {"value": "{base-colors.volksbank.secondary.watermelon-red.50}", "type": "color"}, "warning-moderate": {"value": "{base-colors.volksbank.secondary.apricot-orange.50}", "type": "color"}, "success-bold": {"value": "{base-colors.volksbank.secondary.mint-green.80}", "type": "color"}, "failure-moderate": {"value": "{base-colors.volksbank.secondary.amber-yellow.50}", "type": "color"}, "primary-bold": {"value": "{base-colors.volksbank.primary.blaze-orange.80}", "type": "color"}, "secondary-bold": {"value": "{base-colors.volksbank.primary.endeavour-blue.80}", "type": "color"}, "tertiary-bold": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.80}", "type": "color"}, "attention-bold": {"value": "{base-colors.volksbank.secondary.watermelon-red.80}", "type": "color"}, "warning-bold": {"value": "{base-colors.volksbank.secondary.apricot-orange.80}", "type": "color"}, "success-moderate": {"value": "{base-colors.volksbank.secondary.mint-green.50}", "type": "color"}, "failure-bold": {"value": "{base-colors.volksbank.secondary.amber-yellow.80}", "type": "color"}, "dark-minimal": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}}, "hover": {"primary": {"value": "{base-colors.volksbank.primary.blaze-orange.110}", "type": "color"}, "tertiary": {"value": "{base-colors.volksbank.primary.woodsmoke-grey.20}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.110}", "type": "color"}, "secondary": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "neutral": {"value": "{base-colors.volksbank.neutrals.grey-20}", "type": "color"}, "informative": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}}, "transparency": {"attention-subtle": {"value": "{base-colors.volksbank.opacity.red-20}", "type": "color"}, "attention-minimal": {"value": "{base-colors.volksbank.opacity.red-10}", "type": "color"}, "secondary-subtle": {"value": "{base-colors.volksbank.opacity.blue-20}", "type": "color"}, "secondary-minimal": {"value": "{base-colors.volksbank.opacity.blue-10}", "type": "color"}, "dark-subtle": {"value": "{base-colors.volksbank.opacity.midnight-20}", "type": "color"}, "dark-bold": {"value": "{base-colors.volksbank.opacity.midnight-70}", "type": "color"}, "primary-minimal": {"value": "{base-colors.volksbank.opacity.orange-10}", "type": "color"}, "primary-subtle": {"value": "{base-colors.volksbank.opacity.orange-20}", "type": "color"}, "neutral-moderate": {"value": "{base-colors.volksbank.opacity.neutral-40}", "type": "color"}}, "elevation": {"small": {"value": "{base-colors.volksbank.opacity.midnight-15}", "type": "color"}, "medium": {"value": "{base-colors.volksbank.opacity.midnight-15}", "type": "color"}, "large": {"value": "{base-colors.volksbank.opacity.midnight-20}", "type": "color"}}}, "text": {"display-1": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.d1-48}", "type": "dimension"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-72}", "type": "dimension"}}, "heading-2": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h2-24}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-36}", "type": "dimension"}}, "heading-1": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h1-30}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-48}", "type": "dimension"}}, "heading-3": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h3-20}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-30}", "type": "dimension"}}, "body-1": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b1-16}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-24}", "type": "dimension"}}, "body-3": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b3-12}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-18}", "type": "dimension"}}, "body-2": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b2-14}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-20}", "type": "dimension"}}, "caption-1": {"family": {"value": "{base-typography.volksbank.font-family.asterisk-sans-pro}", "type": "text"}, "size": {"value": "{base-typography.neoshare.size.c1-10}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-16}", "type": "dimension"}}}, "size": {"spacing": {"1": {"value": "{base-numbers.base-1}", "type": "dimension"}, "2": {"value": "{base-numbers.base-2}", "type": "dimension"}, "4": {"value": "{base-numbers.base-4}", "type": "dimension"}, "6": {"value": "{base-numbers.base-6}", "type": "dimension"}, "8": {"value": "{base-numbers.base-8}", "type": "dimension"}, "12": {"value": "{base-numbers.base-12}", "type": "dimension"}, "16": {"value": "{base-numbers.base-16}", "type": "dimension"}, "18": {"value": "{base-numbers.base-18}", "type": "dimension"}, "20": {"value": "{base-numbers.base-20}", "type": "dimension"}, "24": {"value": "{base-numbers.base-24}", "type": "dimension"}, "32": {"value": "{base-numbers.base-32}", "type": "dimension"}, "40": {"value": "{base-numbers.base-40}", "type": "dimension"}, "48": {"value": "{base-numbers.base-48}", "type": "dimension"}, "56": {"value": "{base-numbers.base-56}", "type": "dimension"}, "64": {"value": "{base-numbers.base-64}", "type": "dimension"}, "80": {"value": "{base-numbers.base-80}", "type": "dimension"}, "120": {"value": "{base-numbers.base-120}", "type": "dimension"}, "none": {"value": "{base-numbers.base-none}", "type": "dimension"}}, "border": {"none": {"value": "{base-numbers.base-none}", "type": "dimension"}, "s": {"value": "{base-numbers.base-1}", "type": "dimension"}, "m": {"value": "{base-numbers.base-2}", "type": "dimension"}}, "corner-radius": {"square": {"value": "{base-numbers.base-none}", "type": "dimension"}, "s": {"value": "{base-numbers.base-4}", "type": "dimension"}, "m": {"value": "{base-numbers.base-8}", "type": "dimension"}, "l": {"value": "{base-numbers.base-12}", "type": "dimension"}, "round": {"value": "{base-numbers.base-9999}", "type": "dimension"}}}}, "2-Alias/sparkasse": {"color": {"brand": {"primary": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "dark": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "tertiary": {"value": "{base-colors.sparkasse.primary.dove-grey.100}", "type": "color"}, "secondary": {"value": "{base-colors.sparkasse.primary.tundora-grey.100}", "type": "color"}}, "surface": {"primary": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "secondary": {"value": "{base-colors.sparkasse.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.grey-20}", "type": "color"}, "dark-strong": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "tertiary": {"value": "{base-colors.sparkasse.neutrals.grey-15}", "type": "color"}, "dark-subtle": {"value": "{base-colors.sparkasse.primary.dove-grey.100}", "type": "color"}}, "status": {"success": {"value": "{base-colors.sparkasse.secondary.leaf-green.100}", "type": "color"}, "error": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "failure": {"value": "{base-colors.sparkasse.secondary.supernova-yellow.100}", "type": "color"}, "warning": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.100}", "type": "color"}, "neutral": {"value": "{base-colors.sparkasse.primary.dove-grey.100}", "type": "color"}, "informative": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}, "advanced-editor": {"first": {"value": "{base-colors.sparkasse.tertiary.lilac-purple}", "type": "color"}, "second": {"value": "{base-colors.sparkasse.tertiary.foam-pink}", "type": "color"}, "third": {"value": "{base-colors.sparkasse.tertiary.blizzard-blue}", "type": "color"}, "fourth": {"value": "{base-colors.sparkasse.tertiary.white-blue}", "type": "color"}, "fifth": {"value": "{base-colors.sparkasse.tertiary.conifer-green}", "type": "color"}, "sixth": {"value": "{base-colors.sparkasse.tertiary.tusk-green}", "type": "color"}, "seventh": {"value": "{base-colors.sparkasse.tertiary.chardonnay}", "type": "color"}, "eighth": {"value": "{base-colors.sparkasse.tertiary.derby-orange}", "type": "color"}}, "text": {"tertiary": {"value": "{base-colors.sparkasse.primary.plain-black.60}", "type": "color"}, "primary": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "interactive": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "light": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "secondary": {"value": "{base-colors.sparkasse.primary.plain-black.70}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "success": {"value": "{base-colors.sparkasse.secondary.leaf-green.100}", "type": "color"}, "error": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "warning": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.100}", "type": "color"}, "minimal": {"value": "{base-colors.sparkasse.primary.plain-black.20}", "type": "color"}}, "border": {"default": {"primary": {"value": "{base-colors.sparkasse.primary.plain-black.20}", "type": "color"}, "inactive": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "success": {"value": "{base-colors.sparkasse.secondary.leaf-green.100}", "type": "color"}, "error": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "warning": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.100}", "type": "color"}, "minimal": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "interactive": {"value": "{base-colors.sparkasse.primary.tundora-grey.100}", "type": "color"}, "light": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "strong": {"value": "{base-colors.sparkasse.primary.plain-black.50}", "type": "color"}}, "company-graph": {"ubo": {"value": "{base-colors.sparkasse.primary.tundora-grey.50}", "type": "color"}, "company": {"value": "{base-colors.sparkasse.secondary.leaf-green.50}", "type": "color"}, "person": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.50}", "type": "color"}}}, "icons": {"primary": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "success": {"value": "{base-colors.sparkasse.secondary.leaf-green.100}", "type": "color"}, "error": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "brand-primary": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "interactive": {"value": "{base-colors.sparkasse.primary.tundora-grey.100}", "type": "color"}, "light": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "tertiary": {"value": "{base-colors.sparkasse.primary.plain-black.50}", "type": "color"}, "dark": {"value": "{base-colors.sparkasse.primary.plain-black.70}", "type": "color"}, "minimal": {"value": "{base-colors.sparkasse.primary.plain-black.40}", "type": "color"}, "secondary": {"value": "{base-colors.sparkasse.primary.tundora-grey.100}", "type": "color"}, "warning": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.100}", "type": "color"}}, "background": {"primary-strong": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "secondary-strong": {"value": "{base-colors.sparkasse.primary.tundora-grey.100}", "type": "color"}, "tertiary-subtle": {"value": "{base-colors.sparkasse.primary.dove-grey.20}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "dark-strong": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "primary-moderate": {"value": "{base-colors.sparkasse.primary.scarlet-red.50}", "type": "color"}, "secondary-subtle": {"value": "{base-colors.sparkasse.primary.tundora-grey.20}", "type": "color"}, "dark-subtle": {"value": "{base-colors.sparkasse.primary.plain-black.20}", "type": "color"}, "warning-strong": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.100}", "type": "color"}, "warning-subtle": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.20}", "type": "color"}, "success-strong": {"value": "{base-colors.sparkasse.secondary.leaf-green.100}", "type": "color"}, "success-subtle": {"value": "{base-colors.sparkasse.secondary.leaf-green.20}", "type": "color"}, "failure-strong": {"value": "{base-colors.sparkasse.secondary.supernova-yellow.100}", "type": "color"}, "failure-subtle": {"value": "{base-colors.sparkasse.secondary.supernova-yellow.20}", "type": "color"}, "attention-strong": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-subtle": {"value": "{base-colors.sparkasse.secondary.milano-red.20}", "type": "color"}, "neutral-minimal": {"value": "{base-colors.sparkasse.neutrals.grey-10}", "type": "color"}, "light": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "tertiary-strong": {"value": "{base-colors.sparkasse.primary.dove-grey.100}", "type": "color"}, "dark-moderate": {"value": "{base-colors.sparkasse.primary.plain-black.50}", "type": "color"}, "dark-bold": {"value": "{base-colors.sparkasse.primary.plain-black.80}", "type": "color"}, "primary-minimal": {"value": "{base-colors.sparkasse.primary.scarlet-red.10}", "type": "color"}, "secondary-minimal": {"value": "{base-colors.sparkasse.primary.tundora-grey.10}", "type": "color"}, "tertiary-minimal": {"value": "{base-colors.sparkasse.primary.dove-grey.10}", "type": "color"}, "attention-minimal": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}, "warning-minimal": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.10}", "type": "color"}, "success-minimal": {"value": "{base-colors.sparkasse.secondary.leaf-green.10}", "type": "color"}, "failure-minimal": {"value": "{base-colors.sparkasse.secondary.supernova-yellow.10}", "type": "color"}, "primary-subtle": {"value": "{base-colors.sparkasse.primary.scarlet-red.20}", "type": "color"}, "secondary-moderate": {"value": "{base-colors.sparkasse.primary.tundora-grey.50}", "type": "color"}, "tertiary-moderate": {"value": "{base-colors.sparkasse.primary.dove-grey.50}", "type": "color"}, "attention-moderate": {"value": "{base-colors.sparkasse.secondary.milano-red.50}", "type": "color"}, "warning-moderate": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.50}", "type": "color"}, "success-bold": {"value": "{base-colors.sparkasse.secondary.leaf-green.80}", "type": "color"}, "failure-moderate": {"value": "{base-colors.sparkasse.secondary.supernova-yellow.50}", "type": "color"}, "primary-bold": {"value": "{base-colors.sparkasse.primary.scarlet-red.80}", "type": "color"}, "secondary-bold": {"value": "{base-colors.sparkasse.primary.tundora-grey.80}", "type": "color"}, "tertiary-bold": {"value": "{base-colors.sparkasse.primary.dove-grey.80}", "type": "color"}, "attention-bold": {"value": "{base-colors.sparkasse.secondary.milano-red.80}", "type": "color"}, "warning-bold": {"value": "{base-colors.sparkasse.secondary.pizazz-orange.80}", "type": "color"}, "success-moderate": {"value": "{base-colors.sparkasse.secondary.leaf-green.50}", "type": "color"}, "failure-bold": {"value": "{base-colors.sparkasse.secondary.supernova-yellow.80}", "type": "color"}, "dark-minimal": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}}, "hover": {"primary": {"value": "{base-colors.sparkasse.primary.scarlet-red.110}", "type": "color"}, "tertiary": {"value": "{base-colors.sparkasse.primary.dove-grey.20}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.primary.scarlet-red.110}", "type": "color"}, "secondary": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "neutral": {"value": "{base-colors.sparkasse.neutrals.grey-20}", "type": "color"}, "informative": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}}, "transparency": {"attention-subtle": {"value": "{base-colors.sparkasse.opacity.red-20}", "type": "color"}, "attention-minimal": {"value": "{base-colors.sparkasse.opacity.red-10}", "type": "color"}, "secondary-subtle": {"value": "{base-colors.sparkasse.opacity.tundora-20}", "type": "color"}, "secondary-minimal": {"value": "{base-colors.sparkasse.opacity.tundora-10}", "type": "color"}, "dark-subtle": {"value": "{base-colors.sparkasse.opacity.black-20}", "type": "color"}, "dark-bold": {"value": "{base-colors.sparkasse.opacity.black-70}", "type": "color"}, "primary-minimal": {"value": "{base-colors.sparkasse.opacity.green-10}", "type": "color"}, "primary-subtle": {"value": "{base-colors.sparkasse.opacity.green-20}", "type": "color"}, "neutral-moderate": {"value": "{base-colors.sparkasse.opacity.neutral-40}", "type": "color"}}, "elevation": {"small": {"value": "{base-colors.sparkasse.opacity.black-15}", "type": "color"}, "medium": {"value": "{base-colors.sparkasse.opacity.black-15}", "type": "color"}, "large": {"value": "{base-colors.sparkasse.opacity.black-20}", "type": "color"}}}, "text": {"display-1": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.d1-48}", "type": "dimension"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-72}", "type": "dimension"}}, "heading-2": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h2-24}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-36}", "type": "dimension"}}, "heading-1": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h1-30}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-48}", "type": "dimension"}}, "heading-3": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.h3-20}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-30}", "type": "dimension"}}, "body-1": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b1-16}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-24}", "type": "dimension"}}, "body-3": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b3-12}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-18}", "type": "dimension"}}, "body-2": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "strong-weight": {"value": "{base-typography.volksbank.weight.semibold-600}", "type": "dimension"}, "size": {"value": "{base-typography.neoshare.size.b2-14}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-20}", "type": "dimension"}}, "caption-1": {"family": {"value": "{base-typography.sparkasse.font-family.sparkasse-rg}", "type": "text"}, "size": {"value": "{base-typography.neoshare.size.c1-10}", "type": "dimension"}, "moderate-weight": {"value": "{base-typography.volksbank.weight.medium-500}", "type": "dimension"}, "line-height": {"value": "{base-numbers.base-16}", "type": "dimension"}}}, "size": {"spacing": {"1": {"value": "{base-numbers.base-1}", "type": "dimension"}, "2": {"value": "{base-numbers.base-2}", "type": "dimension"}, "4": {"value": "{base-numbers.base-4}", "type": "dimension"}, "6": {"value": "{base-numbers.base-6}", "type": "dimension"}, "8": {"value": "{base-numbers.base-8}", "type": "dimension"}, "12": {"value": "{base-numbers.base-12}", "type": "dimension"}, "16": {"value": "{base-numbers.base-16}", "type": "dimension"}, "18": {"value": "{base-numbers.base-18}", "type": "dimension"}, "20": {"value": "{base-numbers.base-20}", "type": "dimension"}, "24": {"value": "{base-numbers.base-24}", "type": "dimension"}, "32": {"value": "{base-numbers.base-32}", "type": "dimension"}, "40": {"value": "{base-numbers.base-40}", "type": "dimension"}, "48": {"value": "{base-numbers.base-48}", "type": "dimension"}, "56": {"value": "{base-numbers.base-56}", "type": "dimension"}, "64": {"value": "{base-numbers.base-64}", "type": "dimension"}, "80": {"value": "{base-numbers.base-80}", "type": "dimension"}, "120": {"value": "{base-numbers.base-120}", "type": "dimension"}, "none": {"value": "{base-numbers.base-none}", "type": "dimension"}}, "border": {"none": {"value": "{base-numbers.base-none}", "type": "dimension"}, "s": {"value": "{base-numbers.base-1}", "type": "dimension"}, "m": {"value": "{base-numbers.base-2}", "type": "dimension"}}, "corner-radius": {"square": {"value": "{base-numbers.base-none}", "type": "dimension"}, "s": {"value": "{base-numbers.base-4}", "type": "dimension"}, "m": {"value": "{base-numbers.base-8}", "type": "dimension"}, "l": {"value": "{base-numbers.base-12}", "type": "dimension"}, "round": {"value": "{base-numbers.base-9999}", "type": "dimension"}}}}, "3-Component/neoshare": {"buttons": {"main-set": {"primary": {"color": {"background": {"default": {"value": "{base-colors.neoshare.primary.lima-green.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.lima-green.110}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.110}", "type": "color"}}, "label": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}}}}, "secondary": {"color": {"background": {"attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}}, "border": {"attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.30}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}}, "label": {"attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.neoshare.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.20}", "type": "color"}}, "label": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}, "stealth": {"color": {"background": {"hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}}, "label": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}}, "icon-only": {"primary": {"color": {"background": {"default": {"value": "{base-colors.neoshare.primary.lima-green.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.lima-green.110}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.110}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.neoshare.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.20}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}, "secondary": {"color": {"background": {"hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}}, "border": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.30}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}, "stealth": {"color": {"background": {"hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}}, "fab": {"color": {"background": {"default": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}}}}, "split": {"primary": {"color": {"background": {"default": {"value": "{base-colors.neoshare.primary.lima-green.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.lima-green.110}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.110}", "type": "color"}}, "label": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.neoshare.primary.lima-green.110}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.lima-green.110}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.30}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.110}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.110}", "type": "color"}}}}, "secondary": {"color": {"background": {"hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}}, "border": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.30}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "label": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.10}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.20}", "type": "color"}}, "label": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}}, "action": {"primary": {"color": {"background": {"hover": {"value": "{base-colors.neoshare.neutrals.grey-20}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.ultramarine-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}, "tertiary": {"color": {"background": {"hover": {"value": "{base-colors.neoshare.neutrals.grey-20}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.neoshare.primary.steel-grey.50}", "type": "color"}, "hover": {"value": "{base-colors.neoshare.primary.steel-grey.50}", "type": "color"}, "disabled": {"value": "{base-colors.neoshare.primary.steel-grey.40}", "type": "color"}, "attention": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.neoshare.secondary.sunset-red.100}", "type": "color"}}}}}}, "illustrations": {"business-type": {"color": {"primary": {"value": "{base-colors.neoshare.primary.lima-green.100}", "type": "color"}, "secondary": {"value": "{base-colors.neoshare.primary.fiord-blue.100}", "type": "color"}, "light": {"value": "{base-colors.neoshare.neutrals.white}", "type": "color"}}}}}, "3-Component/volksbank": {"buttons": {"main-set": {"primary": {"color": {"background": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.110}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.110}", "type": "color"}}, "label": {"default": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}}}}, "secondary": {"color": {"background": {"attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}}, "border": {"attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.30}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}}, "label": {"attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.volksbank.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.20}", "type": "color"}}, "label": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}, "stealth": {"color": {"background": {"hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}}, "label": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}}, "icon-only": {"primary": {"color": {"background": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.110}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.110}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.volksbank.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.20}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}, "secondary": {"color": {"background": {"hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}}, "border": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.30}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}, "stealth": {"color": {"background": {"hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}}, "fab": {"color": {"background": {"default": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}}}}, "split": {"primary": {"color": {"background": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.110}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.110}", "type": "color"}}, "label": {"default": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.110}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.30}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.110}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.110}", "type": "color"}}}}, "secondary": {"color": {"background": {"hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}}, "border": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.30}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "label": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.20}", "type": "color"}}, "label": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}}, "action": {"primary": {"color": {"background": {"hover": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.endeavour-blue.100}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}, "tertiary": {"color": {"background": {"hover": {"value": "{base-colors.volksbank.primary.midnight-blue.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.volksbank.primary.midnight-blue.50}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.primary.midnight-blue.50}", "type": "color"}, "disabled": {"value": "{base-colors.volksbank.primary.midnight-blue.40}", "type": "color"}, "attention": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.volksbank.secondary.watermelon-red.100}", "type": "color"}}}}}}, "illustrations": {"business-type": {"color": {"primary": {"value": "{base-colors.volksbank.primary.blaze-orange.100}", "type": "color"}, "secondary": {"value": "{base-colors.volksbank.primary.midnight-blue.80}", "type": "color"}, "light": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}}}}}, "3-Component/sparkasse": {"buttons": {"main-set": {"primary": {"color": {"background": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.110}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}, "label": {"default": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.volksbank.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}}}}, "secondary": {"color": {"background": {"attention-hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.10}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.tundora-grey.10}", "type": "color"}}, "border": {"attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}}, "label": {"attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "default": {"value": "{base-colors.sparkasse.primary.plain-black.110}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.sparkasse.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.primary.scarlet-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.20}", "type": "color"}}, "label": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}}}, "stealth": {"color": {"background": {"hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}}, "label": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}}}}, "icon-only": {"primary": {"color": {"background": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.sparkasse.neutrals.grey-10}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.20}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}}}, "secondary": {"color": {"background": {"hover": {"value": "{base-colors.sparkasse.primary.tundora-grey.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}}, "border": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}}}, "stealth": {"color": {"background": {"hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.20}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}}}}, "fab": {"color": {"background": {"default": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.tundora-grey.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.110}", "type": "color"}}}}, "split": {"primary": {"color": {"background": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.110}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}, "label": {"default": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}}}, "secondary": {"color": {"background": {"hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}}, "border": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}, "label": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.110}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.neutrals.grey-20}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.20}", "type": "color"}}, "label": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}, "separator": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}}}}, "action": {"primary": {"color": {"background": {"hover": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}}}, "informative": {"color": {"background": {"hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}}}, "tertiary": {"color": {"background": {"hover": {"value": "{base-colors.sparkasse.primary.plain-black.10}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.10}", "type": "color"}}, "icon": {"default": {"value": "{base-colors.sparkasse.primary.plain-black.50}", "type": "color"}, "hover": {"value": "{base-colors.sparkasse.primary.plain-black.50}", "type": "color"}, "disabled": {"value": "{base-colors.sparkasse.primary.plain-black.30}", "type": "color"}, "attention": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}, "attention-hover": {"value": "{base-colors.sparkasse.secondary.milano-red.100}", "type": "color"}}}}}}, "illustrations": {"business-type": {"color": {"primary": {"value": "{base-colors.sparkasse.primary.scarlet-red.100}", "type": "color"}, "secondary": {"value": "{base-colors.sparkasse.primary.plain-black.80}", "type": "color"}, "light": {"value": "{base-colors.sparkasse.neutrals.white}", "type": "color"}}}}}, "$themes": [{"id": "1-base-raw-values", "name": "raw-values", "group": "1-Base", "selectedTokenSets": {"1-Base/raw-values": "enabled"}, "$figmaStyleReferences": {}, "$figmaVariableReferences": {"base-colors.neoshare.primary.steel-grey.110": "4ae5fcb52b20608104e6536e231df34f4a5d37ff", "base-colors.neoshare.primary.steel-grey.100": "dbbb9fa8d9144cbadec57f44b11b841608f874d0", "base-colors.neoshare.primary.steel-grey.90": "784926733eb4eaf12d54d2b1fcb1a925fd9589fa", "base-colors.neoshare.primary.steel-grey.80": "14355602e83910951ca9045734427ff5b75ad3e4", "base-colors.neoshare.primary.steel-grey.70": "b0d203bac4f5dbe23124e1157580fb6a7c0148e4", "base-colors.neoshare.primary.steel-grey.60": "164d19bf0eaa6b35d03f94bdf1b40be9eb235ae1", "base-colors.neoshare.primary.steel-grey.50": "0fc679d1ad28389149e37d827b7940158e9d9091", "base-colors.neoshare.primary.steel-grey.40": "53fd5b05b2bdc0ac50e0d5276351c859ec88dba5", "base-colors.neoshare.primary.steel-grey.30": "4e2cd331c6c72b786c6842234bb9140b4aec3884", "base-colors.neoshare.primary.steel-grey.20": "7b3fceaa349015350eb5343ef269bff086ef8509", "base-colors.neoshare.primary.steel-grey.10": "bf44a5338e887c451770cbd353a6b3ab79036fe6", "base-colors.neoshare.primary.ultramarine-blue.110": "8273a230b3224496de7a91014b419d30e9c767c0", "base-colors.neoshare.primary.ultramarine-blue.100": "68ff6e60955131ef3276165afd9a3f25b7f1bf2c", "base-colors.neoshare.primary.ultramarine-blue.90": "7dcf5578eadd31450f557c1e3240952e15e8e590", "base-colors.neoshare.primary.ultramarine-blue.80": "d48c7e3dcc8e241233196c8807fe0a55b35f65fa", "base-colors.neoshare.primary.ultramarine-blue.70": "27d5f8888c7938dc65cf2b5d9e5f4c06e495930e", "base-colors.neoshare.primary.ultramarine-blue.60": "de69e6a4c680680038cee0a3f76df1b9d5f9a6fe", "base-colors.neoshare.primary.ultramarine-blue.50": "8841007b44cd15e25d6b227750d9bf330a2bf19a", "base-colors.neoshare.primary.ultramarine-blue.40": "769a9fbac7310bfc899404eff6041ad80f50ad86", "base-colors.neoshare.primary.ultramarine-blue.30": "a5d51b973dc7fed8ffb11bf84d7a479876f78c84", "base-colors.neoshare.primary.ultramarine-blue.20": "bcae1b3b4b17ce2fba8665aaefc3cc7def081ca5", "base-colors.neoshare.primary.ultramarine-blue.10": "011118f72181c9974b980711eee1d1d111874f6e", "base-colors.neoshare.primary.lima-green.110": "661bb426f9e368c687a6d9c3c1c2269a83d0cb95", "base-colors.neoshare.primary.lima-green.100": "3fda3c59cbad20e952ec05fcd3ea472075e365ee", "base-colors.neoshare.primary.lima-green.90": "a0009c1caa504ac431a6786cb13800412ce0b687", "base-colors.neoshare.primary.lima-green.80": "3e28e92af80917b024564d1d5a95e93fe5635f7c", "base-colors.neoshare.primary.lima-green.70": "2fdd1b5db3f770b65d0942678f4d3c2495b60171", "base-colors.neoshare.primary.lima-green.60": "dec2a6803da0a372b7fcf3754f9dc6462efbea0f", "base-colors.neoshare.primary.lima-green.50": "1818ba14b210589fb8b794b789dc9dce43307a73", "base-colors.neoshare.primary.lima-green.40": "d94e7829ff825e13d8746088996820c7fd33c1d1", "base-colors.neoshare.primary.lima-green.30": "63d027db88e77eda1215686897e7c8f8f8a6eade", "base-colors.neoshare.primary.lima-green.20": "3e61aac9d3ed9c2a4eab157573a11ae50c799cb4", "base-colors.neoshare.primary.lima-green.10": "5577fdb772fa37184a10c98bc175307147cf0b2b", "base-colors.neoshare.primary.fiord-blue.110": "8bc227751e7a45893abe7511a22f0b102f18838d", "base-colors.neoshare.primary.fiord-blue.100": "0cb0f23ac51b99f2eeab063b14425ff528c524a2", "base-colors.neoshare.primary.fiord-blue.90": "eaa78e9119d2b46000c466864e515764108dacf1", "base-colors.neoshare.primary.fiord-blue.80": "45f2143dfbeebde40ead0e11df38d90e1ae6e6d1", "base-colors.neoshare.primary.fiord-blue.70": "7c16e4be7d2eae325d7ddf9492c2634df00664ee", "base-colors.neoshare.primary.fiord-blue.60": "852f9339616b66ff9de5ad8b49d29da91c4bc998", "base-colors.neoshare.primary.fiord-blue.50": "283474e000d6eb148a87a4d5bedbb04d70f6a493", "base-colors.neoshare.primary.fiord-blue.40": "95fcc7447ea3ffaeddd966af448462819a9b9174", "base-colors.neoshare.primary.fiord-blue.30": "970072ef39397e6ce840649118144d431878c8b8", "base-colors.neoshare.primary.fiord-blue.20": "71fbcdecfb48f31cc138403be00352d94de593bc", "base-colors.neoshare.primary.fiord-blue.10": "a2b7856e9be54e140c6e530d436963efb695c198", "base-colors.neoshare.secondary.aqua-green.110": "9ec05e9648ba536d35ae527febe854509b304f09", "base-colors.neoshare.secondary.aqua-green.100": "77cab4215da10928feed2c937831b45fe44871c0", "base-colors.neoshare.secondary.aqua-green.90": "0868729dc2002d712a06cf406e3431cae9400b80", "base-colors.neoshare.secondary.aqua-green.80": "b8ff0c0eafc3f8959f8c278313261cee5255ca16", "base-colors.neoshare.secondary.aqua-green.70": "d1120fcbeb17f87ccce1c0bdd14a35280923eabd", "base-colors.neoshare.secondary.aqua-green.60": "da87e6d56614eec75e41b04cce77a354c62f9760", "base-colors.neoshare.secondary.aqua-green.50": "7f8795ec52c1503b75c857528e3b8f2816fe81bd", "base-colors.neoshare.secondary.aqua-green.40": "308d5d2deb7abd270d81739287cc276b84445f66", "base-colors.neoshare.secondary.aqua-green.30": "6c9e2aa731e0ba1c39ca8dd4cdc6f42cd45bb578", "base-colors.neoshare.secondary.aqua-green.20": "69ed91228b3bd2762b086de271689d84e8e9c8aa", "base-colors.neoshare.secondary.aqua-green.10": "4cbed05734f75665923042859d29b7801340435c", "base-colors.neoshare.secondary.sunset-red.110": "9464b5fe7ec084811dbff248346ad4902188a0f4", "base-colors.neoshare.secondary.sunset-red.100": "8d52f15b401f88e159a3dca3585ee8b515f52091", "base-colors.neoshare.secondary.sunset-red.90": "89a992ce68f1b58045e29376fa4e693000868c95", "base-colors.neoshare.secondary.sunset-red.80": "b5a9a2f2a600de9193dcd2e46925215059e24902", "base-colors.neoshare.secondary.sunset-red.70": "3073fd7a18686ef7eb02444349550ae0d7baab51", "base-colors.neoshare.secondary.sunset-red.60": "f498a9662b9f3e46ef0b1b9a68c706aa07034509", "base-colors.neoshare.secondary.sunset-red.50": "8ef822686a32d80696c1701388c0c025c496990b", "base-colors.neoshare.secondary.sunset-red.40": "86479b94fab6166a37013999cf5d22b000e3d5a3", "base-colors.neoshare.secondary.sunset-red.30": "e9a8207ea41db2d7d9a8e8f3774d998c3e4e996a", "base-colors.neoshare.secondary.sunset-red.20": "f09c16909493e39ad333e0e0d4256ecbb56e4ae6", "base-colors.neoshare.secondary.sunset-red.10": "8ab6c581b0601933eb764c36a2c5567112dfd1c2", "base-colors.neoshare.secondary.corn-yellow.110": "8bd0bd58f72d82d7434e21c15af87f84c7d58896", "base-colors.neoshare.secondary.corn-yellow.100": "a9ebe74ae8d22ed72b2bf1fc741b3380f371d001", "base-colors.neoshare.secondary.corn-yellow.90": "5cd3dd0f16d353733277ee9d72815788acf55780", "base-colors.neoshare.secondary.corn-yellow.80": "1a9b9e5d9affd05a43671ff9b31c64cca6f1b023", "base-colors.neoshare.secondary.corn-yellow.70": "23a050203ac8d9b4dee9f1940d94948205de54da", "base-colors.neoshare.secondary.corn-yellow.60": "84571208ad30cc3f9f7be85f31251cef63911cfd", "base-colors.neoshare.secondary.corn-yellow.50": "95af65e99b21c8bebe590b80cb8ab62787c99221", "base-colors.neoshare.secondary.corn-yellow.40": "5085e39b33fa0f7d655d1c9c2e18dcd986ac1e92", "base-colors.neoshare.secondary.corn-yellow.30": "98a5e735410bd2d65f86326925f99342791ba8eb", "base-colors.neoshare.secondary.corn-yellow.20": "4f1a57bd890619e9375c18d4e033e25df9a364c6", "base-colors.neoshare.secondary.corn-yellow.10": "6504a542f64d180c62221a5bff3fa6af7e928f73", "base-colors.neoshare.secondary.tangerine-orange.110": "3eea4d32216d5f989f3d8ca8d734f554574d93ea", "base-colors.neoshare.secondary.tangerine-orange.100": "f2fbe2a800978a25547eb2001020138b8176842b", "base-colors.neoshare.secondary.tangerine-orange.90": "da912a611571f82f48f3dd6045bf830a9146a77e", "base-colors.neoshare.secondary.tangerine-orange.80": "808077c9f5492a99b63efb52cfc2ae69f02b9977", "base-colors.neoshare.secondary.tangerine-orange.70": "9aee7812fd9e20a597b775224c78f30361224dc4", "base-colors.neoshare.secondary.tangerine-orange.60": "4868adb0f4c75379f67ac9806d85476a5e788d89", "base-colors.neoshare.secondary.tangerine-orange.50": "ec19feb2ab17780da7c2ab830bfaeff9c24fcfd2", "base-colors.neoshare.secondary.tangerine-orange.40": "55aad8e927431229638303f61c13d7d0802ac4b3", "base-colors.neoshare.secondary.tangerine-orange.30": "a1161fa8b38345883989c0edd2e61c600b0dcfe0", "base-colors.neoshare.secondary.tangerine-orange.20": "98fcd81bf33a0ec9a4457aa63cbe6b49e6d54873", "base-colors.neoshare.secondary.tangerine-orange.10": "84c16bdfbaa94e1707d581e66b0c8c47dc754e11", "base-colors.neoshare.tertiary.twilight-purple": "ce5a90b0b6dbbbed3098db8db694e933d0a47c48", "base-colors.neoshare.tertiary.lace-pink": "5c084901865389b3289bf34a95a8951398842cd2", "base-colors.neoshare.tertiary.apple-green": "a04d6ec7ea8a1e79fc0d638428ce4c526acf9282", "base-colors.neoshare.tertiary.yellow-green": "b077401f27e13df8d58adabfaef1e7c80fc99734", "base-colors.neoshare.tertiary.french-blue": "a76df09b075f043ddb79e1dcfbd7ccc133cc4f49", "base-colors.neoshare.tertiary.foam-blue": "4089622fccbf0a4f9255b4347e9e62ceba390f56", "base-colors.neoshare.tertiary.orchid-red": "f86276d3da14d61ac152f7c69f57d20d87c89d98", "base-colors.neoshare.tertiary.peach-orange": "8363a9ac3ffbe53a58b5c46c513de17119e8ec9f", "base-colors.neoshare.opacity.grey-70": "95a92c9ef7a42860163ac5349d9a8be15bfcba00", "base-colors.neoshare.neutrals.white": "a04d42a58691875f04ea87b109f473185610f902", "base-colors.neoshare.neutrals.grey-10": "36f1f2e461f685cf96341ac806f33ee107b06171", "base-colors.neoshare.neutrals.grey-20": "28a9b3e19c490b77301c9755236bd40dd526ff9c", "base-numbers.base-none": "1d9e150514797a8580161cf8021f75ceb15f28b6", "base-numbers.base-2": "edae46ed9e71afdc65f20a8420ec1128544e16ac", "base-numbers.base-4": "ed78462d1268a59b1b91579f3d5065f746438d3a", "base-numbers.base-6": "153d4af3c7c7ea2203817e7acb7d40669b47cad9", "base-numbers.base-8": "db7d243579ca14be9587f379c39d8c07fd7760e3", "base-numbers.base-10": "80b99c32eea1865436d81c0eeab82e649f678584", "base-numbers.base-12": "9bc944342f43df7a1bf882b3c961ff343c758632", "base-numbers.base-14": "d2707358a1977bd221351f95049e05788e0e6020", "base-numbers.base-16": "6d11d45042e008fe32c7e7ca7068364546de877d", "base-numbers.base-20": "944104c68fa65e375208a4cce50f75c5b4fde366", "base-numbers.base-24": "79efcd2572b7bd5e25be5368ea708309b1865fb4", "base-numbers.base-28": "100243f6c7da642c6592f90e79b425e5c1dc28fb", "base-numbers.base-32": "d53bed0482e565aeb7ae6faf394bf09e116442c2", "base-numbers.base-40": "11b9dbb9b1be1be408fd97ba50afe70954708c71", "base-numbers.base-44": "b803b2056da2bf454f1d7c34f10949a37a7db796", "base-numbers.base-48": "a120b8bfd4dc5620ab63c1b8db7b49ae504f0021", "base-numbers.base-56": "96b8a0026cc1f7ec4e38c553670d75c17285db05", "base-numbers.base-64": "3adf2b32bd1e3460a46b3c762301acc2883a7c7b", "base-numbers.base-80": "51e10338860e4f42b4ab6010dccd26f64f7e326e", "base-numbers.base-96": "03ddfa7b7cb368718a4015d8e9dfd4b02257e145", "base-numbers.base-112": "3ec2670523535f5849d85015ae7f1fa2f4d1680e", "base-numbers.base-128": "3439b9dda0afaad43ceb1beb2ca491011e952d66", "base-numbers.base-144": "51548813d5ac707f972078c3c8c58931df2161e6", "base-numbers.base-160": "f0e8702286a1bd9fb5f87a33522e327baf6b3ff5", "base-numbers.base-72": "95f1a2c36d446a648db9e865cf6b0edabd8bf3df", "base-numbers.base-9999": "82f718c526783b93ed261ca8ef8f760b22a09d76", "base-numbers.base-30": "9460147159d2f53b68ccd528e29753304d224053", "base-numbers.base-1": "04c5fe3c158566053ff580f662ee7ec09a3e414a", "base-numbers.base-120": "3d49de7301c261ca8f023f35cb5f10ca4e114abc", "base-numbers.base-104": "b9af055c056a83dacbcf37652f4ae98e4ea43e3e", "base-colors.neoshare.opacity.blue-10": "a006cf80f27f0f573df19f34afd1eb5b3f2b3c23", "base-colors.neoshare.opacity.green-20": "dccc9f71594cbf209507f3ffaeab7f403041dbaa", "base-colors.neoshare.neutrals.grey-15": "088a969f845a4cab09cb03718e0fcc620a914f1c", "base-colors.neoshare.opacity.grey-20": "d1812f3020414811841b65fb19a71518df5cf72f", "base-colors.neoshare.opacity.blue-20": "90b2402420e4d42bff95f7afd41f7f92faa320eb", "base-colors.neoshare.opacity.green-10": "36dceac9e814d1bcbe98c1f4d57d0d5abba6a3ab", "base-colors.neoshare.opacity.red-20": "ad47f715d3d9e63a21e045830cf74fce30dd019a", "base-colors.neoshare.opacity.red-10": "bfaa198439ff2986a00bc5f74ef4676d685245dd", "base-numbers.base-18": "bbf3024393ec6bb1cb5b9b1b752f4dd30f6554ca", "base-typography.neoshare.font-family.chopin": "b3de43b77f941094d720b71be8b8c55d3b3d4e05", "base-typography.neoshare.size.d1-48": "d85d9161bbdbaf9e19f5efd1ad719e02535e8982", "base-typography.neoshare.size.h1-30": "56d118522b8efae28a62dd01444d4be990e85e84", "base-typography.neoshare.size.h2-24": "06b464b950f3100c8585f13aa48503498f072ddb", "base-typography.neoshare.size.h3-20": "0cea557dde5e6d456c96d8e965281700977ee9df", "base-typography.neoshare.size.b1-16": "fe8775b7d37966a68f33a2fa57c6226d8b333dfc", "base-typography.neoshare.size.b2-14": "ecb3d501766e95d36daa96c41db014af540172db", "base-typography.neoshare.size.b3-12": "7596961adb1d6776e06eb3ef0e09cc163ab01fe4", "base-typography.neoshare.size.c1-10": "c5e863efe28fd36d0f5dba8617907e10f7d006df", "base-typography.neoshare.weight.thin-100": "0291b11466db5fed964bf5baa0d9f7a4b7c65329", "base-typography.neoshare.weight.ultralight-200": "05496a7610a679f4a54be97fa5e5da29131a88af", "base-typography.neoshare.weight.light-300": "1d245e323e88af2fc4e5fd9370a25225fe9deeeb", "base-typography.neoshare.weight.regular-400": "62ef82a756a11bc88ddb10b85d50861952e37580", "base-typography.neoshare.weight.medium-500": "3212ff81c6b69204320fe1c8f30460ca9efef485", "base-typography.neoshare.weight.semibold-600": "2c5c8f24cd3ca68d3eaccb5cd696e1f68d59266b", "base-typography.neoshare.weight.bold-700": "17942648c4035c65f399968c8d9f031dcb6db34e", "base-typography.neoshare.weight.extrabold-800": "3a9833d94c20f579ee41a88db3fc630bfa303d5c", "base-typography.neoshare.weight.black-900": "1b3ccb8865e9bc4d05ca0d61c7ea087614b2a74f", "base-numbers.base-36": "2ea5d003e07cf4882eafd3c88eadb1807abca309", "base-colors.volksbank.neutrals.white": "36033377df9cc2851d77a8fbba299177da128c23", "base-colors.volksbank.neutrals.grey-10": "8e4ab2207474c1c306d6da86c1774adf8ec161c1", "base-colors.volksbank.neutrals.grey-15": "d4f2d018865f4f9d6b36346b514c5c3873a955ac", "base-colors.volksbank.neutrals.grey-20": "c98be7a0cf7ac6b49fe5b9cb0442dfc3c2801781", "base-colors.volksbank.primary.midnight-blue.100": "6b1a567174b626b118b8a9249110f577db4616f1", "base-colors.volksbank.primary.midnight-blue.90": "70ccc3fcfb99e816bd4b767bda6e446b654578ac", "base-colors.volksbank.primary.midnight-blue.110": "1a162cbc19facf42f81d8ed9d1e811ad57690038", "base-colors.volksbank.primary.midnight-blue.10": "a91336079d418ef5103da53ccee837f930d988cf", "base-colors.volksbank.primary.midnight-blue.40": "2c41a7e66391ecbc49914e8533a0ba9728b9955a", "base-colors.volksbank.primary.midnight-blue.50": "a456e7cb4e729120a26503d2a859225305b36ff8", "base-colors.volksbank.primary.midnight-blue.80": "844b601824f47635cf5c08f76e366cc569f8b0e7", "base-colors.volksbank.primary.midnight-blue.30": "8e599bb3708d46bcba3e1c0d5d518eb66255dc8e", "base-colors.volksbank.primary.midnight-blue.20": "be0f2ab3c3ade4ce6756d9973bad4001f2186f4e", "base-colors.volksbank.primary.midnight-blue.60": "d1b8fec39a4f3af6e8ae23e74b7d7c754bc30af6", "base-colors.volksbank.primary.midnight-blue.70": "f95007853fd4e585352971629defb2469341fc4d", "base-colors.volksbank.primary.endeavour-blue.20": "df611491f57db5012cdc68be005171f26f836f6d", "base-colors.volksbank.primary.endeavour-blue.30": "ec9a9c710f095a9f2b56348014a638717566ba9a", "base-colors.volksbank.primary.endeavour-blue.50": "8ec994b263b282385ec457c34ede25674627ede0", "base-colors.volksbank.primary.endeavour-blue.40": "f3d91df66d940155d8092bb3ad0447c83ae7e55b", "base-colors.volksbank.primary.endeavour-blue.60": "c8db801820f695b8e83f0a79cd517407511512ea", "base-colors.volksbank.primary.endeavour-blue.110": "8935f730cf9a6336f2bad74a1bc638370b101f3d", "base-colors.volksbank.primary.endeavour-blue.10": "5384311d4b6375a79132356b3ad9b73f4f32562f", "base-colors.volksbank.primary.endeavour-blue.80": "9719d23842a6752adfe8d53e6d66fd40483ec3a5", "base-colors.volksbank.primary.endeavour-blue.70": "86fe8e9b12c9f204cc056c398eecca70ffcf517b", "base-colors.volksbank.primary.endeavour-blue.90": "889fa5312e0ef5a81cccd6da875059743e594b77", "base-colors.volksbank.primary.endeavour-blue.100": "e23f5f06677dbcba128eb997a07f3b6372306952", "base-colors.volksbank.primary.blaze-orange.40": "cc4e40335b9664da1d202e6a03eebc87c3facb1e", "base-colors.volksbank.primary.blaze-orange.60": "ee4bf860fe5d3aeb4deeadf928f982921dc919e3", "base-colors.volksbank.primary.blaze-orange.20": "99a0d7fc9f072d81742261fa5c0089952ef8db27", "base-colors.volksbank.primary.blaze-orange.30": "6fbd1789f84d618e86d5cb045d10df2ccf8644e1", "base-colors.volksbank.primary.blaze-orange.50": "8f2d91edbf34fbb1da7f0d40ea73eef1c02dbe65", "base-colors.volksbank.primary.blaze-orange.80": "7e6aa57c267df90991926bde90ad1210ff73e1ae", "base-colors.volksbank.primary.blaze-orange.70": "dea4296d4dccecb165a6d1d4cc439eac3416dbca", "base-colors.volksbank.primary.blaze-orange.10": "3c4118e3558144853b630ff6b99260524ef887a8", "base-colors.volksbank.primary.blaze-orange.110": "7dc064bc5a1e0135d290149ff8e55768d0aa76a2", "base-colors.volksbank.primary.blaze-orange.90": "65ad40d7e6a51ba90b02fb97dcce85af8ae366f2", "base-colors.volksbank.primary.blaze-orange.100": "e2693a0aa10dc9481dd53c0cead9a2c7055d76e4", "base-colors.volksbank.primary.woodsmoke-grey.20": "ab9f488634c43e4d9a822ee8caff5111efad0393", "base-colors.volksbank.primary.woodsmoke-grey.50": "5b0dd50c8539390892f99fe26e02856e7ea52f2f", "base-colors.volksbank.primary.woodsmoke-grey.70": "ac729fb3956cc2c915110392a1586ad107f6d0de", "base-colors.volksbank.primary.woodsmoke-grey.90": "9ca1e41d992f59460fd4378e084c798afbceecb8", "base-colors.volksbank.primary.woodsmoke-grey.30": "8ff898785bba57e5134d237ebed73466f3a5ed75", "base-colors.volksbank.primary.woodsmoke-grey.10": "1e896334b3aba0eec40be7c8b1a23388cec1bc30", "base-colors.volksbank.primary.woodsmoke-grey.100": "4ce468f0255374aa5f37395cd7ed42741496d27e", "base-colors.volksbank.primary.woodsmoke-grey.60": "04966cfd798089694af47e63b5c18ab948ac7bd0", "base-colors.volksbank.primary.woodsmoke-grey.80": "d9ac386a931677ea70a49dc7e43c335bc4d99f0d", "base-colors.volksbank.primary.woodsmoke-grey.40": "1225bf3391dcb09784229b673fb43cde3e151eef", "base-colors.volksbank.primary.woodsmoke-grey.110": "2e597ac55c7a8747f542da04cad9a48701cfb3df", "base-colors.volksbank.secondary.mint-green.30": "91471035892ad99b38a180b0f3bc8b269b2c83be", "base-colors.volksbank.secondary.mint-green.20": "cb7cab09dc6807603819732b99a3448a1a922024", "base-colors.volksbank.secondary.mint-green.40": "2d18781f56c106ee1738d772e52f44521c8a88d2", "base-colors.volksbank.secondary.mint-green.90": "9d4adbb8f130a34ca8ede20a8d3bebb1f66a42b8", "base-colors.volksbank.secondary.mint-green.10": "a63dde6721959113f072227bbf158ebc4ec79637", "base-colors.volksbank.secondary.mint-green.100": "ec5f335fe58efc376ecafbefa3ec705af7be6b77", "base-colors.volksbank.secondary.mint-green.50": "96fa8c1cf3512ada3fe8080ea6c72827498ce2c8", "base-colors.volksbank.secondary.mint-green.60": "ad9df3c8bcbeb2d7898317d24cd2ce191002118b", "base-colors.volksbank.secondary.mint-green.80": "4c0f522ed2920af55f6b802f89347ca1239e0077", "base-colors.volksbank.secondary.mint-green.70": "4ade80294157f3b8b70eccb39e85cf3e22a06e1f", "base-colors.volksbank.secondary.mint-green.110": "81b6c710f56df50b7077cc3a6e8853589e9caa25", "base-colors.volksbank.secondary.watermelon-red.30": "327275eef9802ad4ae64e415ad86da3c7a3558be", "base-colors.volksbank.secondary.watermelon-red.40": "5625ad7b49df2adacc07510fb762bf71fc3dae98", "base-colors.volksbank.secondary.watermelon-red.50": "ffcc581702963d05dd185f19f12444459c488dde", "base-colors.volksbank.secondary.watermelon-red.60": "ba8f98f19184f3c2c380c369214ea3061aae862d", "base-colors.volksbank.secondary.watermelon-red.80": "933b61c1be654dbeace8cd5036be2bc0fee21754", "base-colors.volksbank.secondary.watermelon-red.90": "8b2c0bf43944710aa0d8a2f7beb03a9f50b9e27b", "base-colors.volksbank.secondary.watermelon-red.20": "7e25854710a4247f1fbf8f3d2d68de1d2f221d9c", "base-colors.volksbank.secondary.watermelon-red.10": "3cf04fbf3b62ad2377d8e07e5331f13127ec1c9c", "base-colors.volksbank.secondary.watermelon-red.100": "247664d4d30773d5584b206b27a5bd8c23508511", "base-colors.volksbank.secondary.watermelon-red.70": "a52c356f11dd76dab332f3b592d678103909f254", "base-colors.volksbank.secondary.watermelon-red.110": "93e11c2dc13e703538bef904c2e608da4ccf5bd4", "base-colors.volksbank.secondary.amber-yellow.60": "50846c660c0c17885efd90e73f7240c218ea065d", "base-colors.volksbank.secondary.amber-yellow.80": "040fa5c5b5dd22d1f9569582e9cb9b8abe3d2dea", "base-colors.volksbank.secondary.amber-yellow.30": "aa2101318b9e2e968ec198ee5c2fc62f93354ca3", "base-colors.volksbank.secondary.amber-yellow.40": "9f3a516517fc392776ebea8810d5c3a39afcebc2", "base-colors.volksbank.secondary.amber-yellow.90": "670f4aba3f506b9570840f047c76da787f747266", "base-colors.volksbank.secondary.amber-yellow.20": "798083653447f7d3b7edc5b023a9f7475742a89b", "base-colors.volksbank.secondary.amber-yellow.100": "d83670035b86091033cd62e119bc0d6d1e739413", "base-colors.volksbank.secondary.amber-yellow.70": "0cb2798807c9bd58afc888c1fe45d30c0ce65dbb", "base-colors.volksbank.secondary.amber-yellow.10": "a8daff69e574f2c562514208fa7726847a671a4d", "base-colors.volksbank.secondary.amber-yellow.50": "a4b5ac8dba5a972d281027358521da9bbe081038", "base-colors.volksbank.secondary.amber-yellow.110": "053a8010465c9430703e2f3846a8eb88b2e2b0ea", "base-colors.volksbank.secondary.apricot-orange.20": "83d6eeea2bbdb29974eac8c7985bed1ac7949323", "base-colors.volksbank.secondary.apricot-orange.30": "873b7be53a59e0c9c58da53c5c029a711dd5d873", "base-colors.volksbank.secondary.apricot-orange.70": "7d6e09d2315fed0147c4323bc20f8a846728306f", "base-colors.volksbank.secondary.apricot-orange.50": "67fce02e99f42cbbd788c0df833524612d2c7645", "base-colors.volksbank.secondary.apricot-orange.90": "b412e036cf087067935894142e66bea18ea82b53", "base-colors.volksbank.secondary.apricot-orange.10": "73b2b0b3d3dec05b0945f62a77a4c5553e4624a2", "base-colors.volksbank.secondary.apricot-orange.40": "a67e24d4aa10703d82bed697252decb8673a8228", "base-colors.volksbank.secondary.apricot-orange.80": "e46630517785d4e6ae29222cd7c99f2dcbcde9f9", "base-colors.volksbank.secondary.apricot-orange.60": "89206c69601ecdd0ae322decec07661ea21b10f4", "base-colors.volksbank.secondary.apricot-orange.100": "cfe0ee8da381951f8129386e38d1ebd9260251b1", "base-colors.volksbank.secondary.apricot-orange.110": "8bed3708205b4859a4d66b029965316e1bff29c6", "base-colors.volksbank.tertiary.viola-pink": "f8400167db874c8ea38163e601f79f157679ed70", "base-colors.volksbank.tertiary.prim-pink": "b8a0aef546cfa7d1372fd04569f6f4595fd35143", "base-colors.volksbank.tertiary.lavender-purple": "31a10a0f60ccd3a9ab316260ad921a3ec0277ba4", "base-colors.volksbank.tertiary.water-blue": "394bcebb55916a906e6e07d9813114bc35f6c0d0", "base-colors.volksbank.tertiary.sisal-brown": "b8a765eaf32e3019ad1870ad104e6b98317d6e9e", "base-colors.volksbank.tertiary.malibu-blue": "072d2b76a99634763b803cdb426aa673c4b97a2f", "base-colors.volksbank.tertiary.london-purple": "9016c5c2341e7887a3829e58a804cbd082fc6e1c", "base-colors.volksbank.tertiary.avocado-green": "bcec3a985cd5d9e5062ad29378177e905299af6e", "base-colors.volksbank.opacity.orange-20": "b6fef9c20e4518f131d51ce288efac5583e5789d", "base-colors.volksbank.opacity.blue-10": "126d685bfaecf22f29536cc5e6616b4638834315", "base-colors.volksbank.opacity.midnight-70": "f2be559dce43ad99cda3410e1cc0abf754780998", "base-colors.volksbank.opacity.orange-10": "966f5fd97613f9e8988bddd3ddec07ab9afa0e14", "base-colors.volksbank.opacity.red-20": "5c4156987d526b9f16bab8479e0ccbe4e9b65d05", "base-colors.volksbank.opacity.blue-20": "2d93e8b8889331aca2b2a84d9bd48fff8f3f3615", "base-colors.volksbank.opacity.red-10": "f01160533b4698fe0529ce89325721af291d4467", "base-colors.volksbank.opacity.midnight-20": "11e8accd5b33364b7e6b2e5f421b66abbbdcd88a", "base-typography.volksbank.font-family.asterisk-sans-pro": "7ac42be7d14828aae97b057bccd4851691689727", "base-typography.volksbank.weight.thin-100": "f1243972820518c23faa601d4b5069cf08237dce", "base-typography.volksbank.size.d1-48": "7bbf37e005962715e21245063edfe1c8b6438a83", "base-typography.volksbank.size.h1-30": "713f65cd120a7e1be26c718d05d49aa7b4513ec8", "base-typography.volksbank.size.h2-24": "25af340457652b9bd29023de5678c754b198a242", "base-typography.volksbank.size.h3-20": "7c7a36ae5ca951bba45e7aeda388c3bcf27869a6", "base-typography.volksbank.size.b1-16": "c3777708f4a2ffa011589a949b079e9c65d3e713", "base-typography.volksbank.size.b2-14": "e88517c27fd635c72d4e5e97b6b4f075a7453ecd", "base-typography.volksbank.size.b3-12": "2adbe87661ff8ea93e89b6b336b4fcfa05e49a95", "base-typography.volksbank.size.c1-10": "e08a150922b9997441e635fdf6dc9d5ec0043cee", "base-typography.volksbank.weight.ultralight-200": "12845ef6d4ab8e46864069c49a5fb55fafb2b1c3", "base-typography.volksbank.weight.light-300": "57719d11c93a9a4f5101453a2c8c62bfe9d3ab45", "base-typography.volksbank.weight.regular-400": "63fdeb89b853b82c04b733093049792dbc3593ff", "base-typography.volksbank.weight.medium-500": "3abff496349c6801564a1312c3a9d0bd3e0e066c", "base-typography.volksbank.weight.semibold-600": "936cc1c8f3259eba2801f963d4d92552373aac97", "base-typography.volksbank.weight.bold-700": "dff75f3735d9da76ac18187ae866f4411f767a25", "base-typography.volksbank.weight.extrabold-800": "3302dc9279c334e2317ef7d506dccd34cf45cccb", "base-typography.volksbank.weight.black-900": "9541e84b451dccce575be612870f28279232cca9", "base-colors.neoshare.opacity.grey-15": "fe8ee21fe82c0da48dab8433b1e180e2fb9bffbd", "base-colors.volksbank.opacity.midnight-15": "1f151dd388ffb334ddb64421b8e94ef5808535a2", "base-colors.volksbank.opacity.neutral-40": "0f108d02c3db2e60ba85917264c6c242de432bf4", "base-colors.neoshare.opacity.neutral-40": "4f373f6566c5fd593653e2b912d8f2fc7e61eaed", "base-colors.sparkasse.neutrals.white": "c5a14b03f6f997ee938f2aad64d68ed4041732ee", "base-colors.sparkasse.primary.plain-black.110": "53aae80ad89a37ada1e4a07686d2420b7e4b4070", "base-colors.sparkasse.primary.plain-black.100": "83d30e042356f4aeb73046c062c665f1f4d355f9", "base-colors.sparkasse.primary.plain-black.90": "41bf1aed28f59fca65d5cd02dd3109b4a5cc6c43", "base-colors.sparkasse.primary.plain-black.80": "0e2953df3c1bfaeb79b36ef3845772e95dd8dd77", "base-colors.sparkasse.primary.plain-black.70": "cb2c285540393c6f41382a7eaa53a0a05caeb0ed", "base-colors.sparkasse.primary.plain-black.60": "04a918a21d7ed802c787bd1dc8595ec8f1e679ce", "base-colors.sparkasse.primary.plain-black.50": "6305ae88260350449f1fa9a0882364972245dece", "base-colors.sparkasse.primary.plain-black.40": "7b195ba0814fcfa4b3b303df1a959a49144602eb", "base-colors.sparkasse.primary.plain-black.30": "1db0571f983d1ac526f9a69d39fedb9e18bcb797", "base-colors.sparkasse.primary.plain-black.20": "db0227ab3494ae6b350afa63354cd5257282dcc2", "base-colors.sparkasse.primary.plain-black.10": "653c8e298f102662530da4e22f0ec5fa90916b01", "base-colors.sparkasse.neutrals.grey-10": "7dd8e5acd2d572d8b7a408817f5632dbb0eac4c8", "base-colors.sparkasse.neutrals.grey-15": "f0d8d5bc65aab43c93f3b0f2aafe063d37fbc92c", "base-colors.sparkasse.neutrals.grey-20": "390d9f650c0bbf049436f901ac737d858c262324", "base-colors.sparkasse.primary.tundora-grey.110": "cea07b558a75200f98d7485702da70c909d77f9a", "base-colors.sparkasse.primary.tundora-grey.100": "3bbd67c5ce8f60c53ee83a903fce1d671236839f", "base-colors.sparkasse.primary.tundora-grey.90": "d7c3ac558c8619c41324d2f328ea7f07ce83e7be", "base-colors.sparkasse.primary.tundora-grey.80": "a5d6da310d6f4e8884f5a3445d9720526a705a97", "base-colors.sparkasse.primary.tundora-grey.70": "8c3f65d49683096b2846a9e900c1c134153f4ea2", "base-colors.sparkasse.primary.tundora-grey.60": "e592ddbb421338a1567e33e5b5d725ddbbe0e88d", "base-colors.sparkasse.primary.tundora-grey.50": "9b22de3143b1ee61ee955f268cf056af508a5bca", "base-colors.sparkasse.primary.tundora-grey.40": "178a9fb420a623d8623696178d2003210f872e2d", "base-colors.sparkasse.primary.tundora-grey.30": "0d030860319d2a759be18bca4c02a0feb067f984", "base-colors.sparkasse.primary.tundora-grey.20": "e7fb2e51014b54fa1f68c4d51f10b78c455a2922", "base-colors.sparkasse.primary.tundora-grey.10": "a1e2176e9ef1c213db6dd39eb39db18bcabd6951", "base-colors.sparkasse.primary.scarlet-red.110": "72df65e9514b66d6b9e00ccfe3c7497c602fec6a", "base-colors.sparkasse.primary.scarlet-red.100": "9923ccaa939db6c23b4af762ba917f011b5d3986", "base-colors.sparkasse.primary.scarlet-red.90": "c05ef5ab650af75a0079df15caa9941aaeeec2ec", "base-colors.sparkasse.primary.scarlet-red.80": "2b062510797a9787937fdb6943d8979a82f63049", "base-colors.sparkasse.primary.scarlet-red.70": "786c12befac41aa8f60160920612fb64a72adc62", "base-colors.sparkasse.primary.scarlet-red.60": "464cbf4579f3022b8aea3bdb21210cb96f27b0de", "base-colors.sparkasse.primary.scarlet-red.50": "fe832547b297720a25168302d41e7d7322169fc9", "base-colors.sparkasse.primary.scarlet-red.40": "8cfdbb3cfe1f226a24e04e26468579741c44c580", "base-colors.sparkasse.primary.scarlet-red.30": "fad70182e012c115d86d3754986a6f5e362743d1", "base-colors.sparkasse.primary.scarlet-red.20": "d2dd95680c981c0172bf426f71efd47639d770a5", "base-colors.sparkasse.primary.scarlet-red.10": "140c5ad6f07ab524ad98ba1083658f236d9b05f2", "base-colors.sparkasse.primary.dove-grey.110": "179560f60be163d94e52cc0596afd006881eaa4b", "base-colors.sparkasse.primary.dove-grey.100": "5160aaaff73bc59061dae2dfa57963edbc2929ab", "base-colors.sparkasse.primary.dove-grey.90": "15b50848da3f3484d1e3ca464b81eac5ca1917ce", "base-colors.sparkasse.primary.dove-grey.80": "86cd0d9e8eb413d6f5e69b67a303569e27e2fc39", "base-colors.sparkasse.primary.dove-grey.70": "9f5697a6f87374f70a07563d4f92d53a1085baa3", "base-colors.sparkasse.primary.dove-grey.60": "2de0c280c2330f36e3027bef61d6b4f537ee887e", "base-colors.sparkasse.primary.dove-grey.50": "542a438e222eecee33e29c54ae4db5b99e23a246", "base-colors.sparkasse.primary.dove-grey.40": "94925523e39c2dc3094767e8ff421874a500a4f6", "base-colors.sparkasse.primary.dove-grey.30": "c2440c7d3f2c407edbba1f8c495fd9bb56972f0d", "base-colors.sparkasse.primary.dove-grey.20": "edce833d3d84bd23ad181a703255d3ca66e666f5", "base-colors.sparkasse.primary.dove-grey.10": "a95a4b58cec1f5d5452e90fea58aa71e374625bf", "base-colors.sparkasse.secondary.leaf-green.110": "c21dde69a52ef5cf7b568e1ab57672ed044f0937", "base-colors.sparkasse.secondary.leaf-green.100": "7e118a85880ba4ef56ceab4dac391e14d6edad9b", "base-colors.sparkasse.secondary.leaf-green.90": "46a92b9077bc91894cf71dd00c689bcd39b3c5ea", "base-colors.sparkasse.secondary.leaf-green.80": "04977f8d2881d902dad661618a9200755fde0b5e", "base-colors.sparkasse.secondary.leaf-green.70": "98bd43bbda7a64a36c424ea2483aa6592c76d73b", "base-colors.sparkasse.secondary.leaf-green.60": "7e619490545b6e1427a785fc9b889278b0913e40", "base-colors.sparkasse.secondary.leaf-green.50": "b5384e54791d056a510ef2aed244b9061d3a68b7", "base-colors.sparkasse.secondary.leaf-green.40": "47cdd11cb7f26b46376a92a6677c1808cff6095a", "base-colors.sparkasse.secondary.leaf-green.30": "260a88425b250d080b66960f6c640beb37accefc", "base-colors.sparkasse.secondary.leaf-green.20": "2c5c4a9c52f1c74c66666a1ef4bfc54863cb1da0", "base-colors.sparkasse.secondary.leaf-green.10": "e0218e4b112c3234b76905f0dabf86f98fc17557", "base-colors.sparkasse.secondary.milano-red.110": "5fae45b24a757f700aa5f9aaa22df4078ee52926", "base-colors.sparkasse.secondary.milano-red.100": "d5f400f33324b9ae4a38bae3151c8777736fe77e", "base-colors.sparkasse.secondary.milano-red.90": "926c9573c25c92f2038aa749dc052f3dd9bb0b94", "base-colors.sparkasse.secondary.milano-red.80": "9cda619a219d6f2fb955f789f2505da9aeea3f80", "base-colors.sparkasse.secondary.milano-red.70": "a5455bd43aca941312df15e79ea1ede2e263bc06", "base-colors.sparkasse.secondary.milano-red.60": "b0e6fe9f79cb992b6f4a4aa314807cc393bbab9b", "base-colors.sparkasse.secondary.milano-red.50": "5504542a7398b02e6e2c2d362ae666dc9b8a022d", "base-colors.sparkasse.secondary.milano-red.40": "a5595fa5a2b4ff90507c5baab662ac7fb6aa8321", "base-colors.sparkasse.secondary.milano-red.30": "6bd2248d39d0032602aee3e219802cb5c83de3ef", "base-colors.sparkasse.secondary.milano-red.20": "7732e280572bb20ab1fd52bacc8198d3dce05138", "base-colors.sparkasse.secondary.milano-red.10": "8ee37a6e5417f93e8107afdb6b6cf464e3ebf79e", "base-colors.sparkasse.secondary.supernova-yellow.110": "f7e523ee286a9ed7acc037603880fdb3943116e8", "base-colors.sparkasse.secondary.supernova-yellow.100": "15ee1c72b3e5c4f8dc3bfdb026ec77f1907b2940", "base-colors.sparkasse.secondary.supernova-yellow.90": "a27c73ddf0bb00795b9714e1cbdfbee6c4bba468", "base-colors.sparkasse.secondary.supernova-yellow.80": "08a07b21b70116a632f360878542406d5178f8fc", "base-colors.sparkasse.secondary.supernova-yellow.70": "336eef95ae0db9ce5d1f21f864705ec5df9681c9", "base-colors.sparkasse.secondary.supernova-yellow.60": "6d9a22dcab053a2feb183dd8064daef3c9f818f1", "base-colors.sparkasse.secondary.supernova-yellow.50": "f13d280c8dd6e33e2d8c643298baf2b1d7eda8fa", "base-colors.sparkasse.secondary.supernova-yellow.40": "5981cff917ef2794e6d06910aee2a0b7595bf55c", "base-colors.sparkasse.secondary.supernova-yellow.30": "023a8c3f6d39950730e3d49d0d5884103bedf04b", "base-colors.sparkasse.secondary.supernova-yellow.20": "5562d5f96ef56c372c9db7e183fd2c7fadd4aba3", "base-colors.sparkasse.secondary.supernova-yellow.10": "aafff275e9092e4a98506103ecc72d3f98d1724d", "base-colors.sparkasse.secondary.pizazz-orange.110": "6506bd8a642d6c2876e7d1c53b8af6101350cd11", "base-colors.sparkasse.secondary.pizazz-orange.100": "f383d6725fa89f507e1bfe4fdabf223c6603a9c5", "base-colors.sparkasse.secondary.pizazz-orange.90": "1fd13b50ee0d8742f062e55f5b98edda69b6a6a9", "base-colors.sparkasse.secondary.pizazz-orange.80": "c771bdda8b51c026841387ee461acd4b801695b9", "base-colors.sparkasse.secondary.pizazz-orange.70": "54e3686bd941738581252d0ca70c245940d90e2f", "base-colors.sparkasse.secondary.pizazz-orange.60": "a46422db9800c07da430a73765f68309d6f83c9b", "base-colors.sparkasse.secondary.pizazz-orange.50": "503bfec83d852ca97f0f2c88bd0c9c86224a479a", "base-colors.sparkasse.secondary.pizazz-orange.40": "83274dac92df177b89c2638cd32efb02e7ece185", "base-colors.sparkasse.secondary.pizazz-orange.30": "875aeebe23bbc93ef156c8896e9497fbb7eb1c33", "base-colors.sparkasse.secondary.pizazz-orange.20": "ddf01373ba9275a31dec79f34e59e9d1a3565238", "base-colors.sparkasse.secondary.pizazz-orange.10": "763f2a4a601d6367655fb4dce7bfd877275bcfe5", "base-colors.sparkasse.tertiary.blizzard-blue": "46fdc59a761f6660bf7483ce8dc164b82749c500", "base-colors.sparkasse.tertiary.white-blue": "8c9c894ce2f4e4bd69f2e66ad289660929d4b4fb", "base-colors.sparkasse.tertiary.conifer-green": "6f425a2ef33a147f59cc0db0b5d84578c72a4825", "base-colors.sparkasse.tertiary.tusk-green": "3125131a1f4c31fab942514a7d3b8917bc289f80", "base-colors.sparkasse.tertiary.derby-orange": "5ba2c3b9eb1f376c3b015f476d36e683430a615c", "base-colors.sparkasse.tertiary.chardonnay": "e96f40af07a4bb352b77de8c5a86a878aaa52f58", "base-colors.sparkasse.tertiary.lilac-purple": "5b9ffe63f5220ee17b90efd8fff438efac1fd1e2", "base-colors.sparkasse.tertiary.foam-pink": "02de6681f576dbffea0f696325a171bed387b828", "base-colors.sparkasse.opacity.neutral-40": "0ee111b5817bfa5502af42058f520b5b0a7bd8f9", "base-colors.sparkasse.opacity.black-70": "2714d5f315ee15f59c9c4311952694d33474c9ff", "base-colors.sparkasse.opacity.black-20": "9d16034f29507224c91cb2524a8202b750ec71ff", "base-colors.sparkasse.opacity.black-15": "69fa09beb049468f72258c4a09e6f4a6336436ca", "base-colors.sparkasse.opacity.tundora-20": "adc0469c7ef56805bf1237e12a239ea5c8edacf0", "base-colors.sparkasse.opacity.tundora-10": "f1148c872d0a301488cb7f2908128a0e4395c2b0", "base-colors.sparkasse.opacity.green-20": "95969b021d4af9df81e8522ba911041599723f3f", "base-colors.sparkasse.opacity.green-10": "c23b88d3e830465e3e12a761fd96c2a56e75a0b6", "base-colors.sparkasse.opacity.red-20": "381390146fa3abc83fa4e856939cf7104e722ca9", "base-colors.sparkasse.opacity.red-10": "ad72717355eef71a7420f62f606b8315cfb07522", "base-typography.sparkasse.font-family.sparkasse-rg": "4fe3fc1ce9b1344e5260ee76cce7966481a0d63e", "base-typography.sparkasse.weight.thin-100": "b8384b61a140727ead23c305fd10cfad96fff2a7", "base-typography.sparkasse.weight.ultralight-200": "bc615dbc1a1394999b710b8d6b01c351010462bc", "base-typography.sparkasse.weight.light-300": "45689beab5afee3178c7f274d50645817708b018", "base-typography.sparkasse.weight.regular-400": "53821bf1496544c4ff2d8f4d11c198d454cdaa15", "base-typography.sparkasse.weight.medium-500": "2cab2cf6ff44566a74560297c6186315ce2419f2", "base-typography.sparkasse.weight.semibold-600": "776c75008b01b748f94fe23dfe6d07d37b337c6e", "base-typography.sparkasse.weight.bold-700": "9cae80518020d2da7874f7f9da9039e24a31b552", "base-typography.sparkasse.weight.extrabold-800": "b330cafeb14509d5890fe7db0286b4f88e726f86", "base-typography.sparkasse.weight.black-900": "4609b88e9831cc6665134c679494deb6a40ff5db", "base-typography.sparkasse.size.d1-48": "03e0d98a09c593157f39dbe16572607c058645eb", "base-typography.sparkasse.size.h1-30": "4221d2f2280eedee1f0787cbdb3fa0e551aa56e4", "base-typography.sparkasse.size.h2-24": "8d24935e8ca88a305946ae414eca4f60e1c82b95", "base-typography.sparkasse.size.h3-20": "1013ad905de0dd864b16346082bbbcb347de7296", "base-typography.sparkasse.size.b1-16": "920b37ede24fb893e50033b7de5ac9c0ac743d15", "base-typography.sparkasse.size.b2-14": "b85dc9c439ca295dd1247714954874a56161167c", "base-typography.sparkasse.size.b3-12": "3f30d2b6e3d38768a455437bff57ac3a989d057a", "base-typography.sparkasse.size.c1-10": "cb74e1db7cd71a625ccbd6b1106b84caad41c909"}, "$figmaModeId": "4766:0", "$figmaCollectionId": "VariableCollectionId:4766:11479"}, {"id": "2-alias-<PERSON><PERSON><PERSON>", "name": "sparkasse", "selectedTokenSets": {"2-Alias/sparkasse": "enabled"}, "$figmaStyleReferences": {}, "$figmaVariableReferences": {"color.brand.primary": "7a595493d51bbce68c2256ae0824e9901dab77fc", "color.brand.dark": "d7cebcf0f2cf00b5360f193e1d8a6e0820928502", "color.brand.tertiary": "5f7939a94ef3f8c7baa88be056489189155c40c5", "color.brand.secondary": "6f70e17e8e9d5ca74affa719429be2a25ec7d3f8", "color.surface.primary": "43006e9bc2e917cec736d66a505c3e43e03643ae", "color.surface.secondary": "d620287b5aa9e3f1fccda24f8196563a26fbd75b", "color.surface.hover": "578bcab358b56a13b08643aaf2dfa697615c6d3b", "color.status.success": "9413efdcae8c9609190ec02e96e0a1a80f604928", "color.status.error": "52c63409b2d57fd4173bb8ce66920b54232f7fe5", "color.status.failure": "4d83e43dc9c411168354d0d5b03621038e812dc5", "color.status.warning": "2b6b8362e5de9c92cc086c32b5eb4c0ccbc2268e", "color.advanced-editor.first": "8122ff21744f4bcd501efd19f34b7b37351a101e", "color.advanced-editor.second": "66922e1c92633194341ee145d88ba6aba5f3164b", "color.advanced-editor.third": "9a910566b6d7ddb6e88af9df6a83aa1576f8f278", "color.advanced-editor.fourth": "39f9c9299bb0adcf1e06686c1cdc5238718eabd6", "color.advanced-editor.fifth": "138c4bb13f97596394235c46f66ddcf87056bff6", "color.advanced-editor.sixth": "d869309ca5016e83ea4245382b8aa101cf65b01e", "color.advanced-editor.seventh": "491e67da835cfcef4890fed054b71331b55f0112", "color.advanced-editor.eighth": "8ce46e05757cb4eae271dbbc6297622a8c51730e", "color.status.neutral": "fd76107eab94a41820dbdc6ca1b9061dbf0d7e1a", "color.status.informative": "f0b2fe02f2eb587a97c4d3468eb0d617f7172c1c", "color.surface.dark-strong": "600d29e8ada6303ff94b135d6c95e0694088123d", "color.text.tertiary": "81949ba703642f17e8dc89d873a42f56b4868938", "color.text.primary": "d08e8f3c7c79de97dedd82a96b7689a9c5fc234a", "color.text.interactive": "d94a119c1224e0795facad53f47b33e4801c9cf1", "color.text.light": "b15a65aa4b09782b80db2867621cad91202537ac", "color.text.secondary": "51e5060863602e4d48e0af2bc0199509984b0f4d", "color.surface.tertiary": "27acfe52e842d58ae25e9726ba2aaf1cc6115f5f", "color.surface.dark-subtle": "fa74bcab2fc59cd4b0c202e847bb2e46741ede5a", "color.border.default.primary": "d47f3250ec89f0f08ebad0cdc9ca536b08017164", "color.icons.primary": "11d3797c54235cc4de5e4eca91a582bbc4d161a3", "color.icons.disabled": "3858b9ca49dbb6dc8c0ad0676afd2967471d1dad", "color.border.default.inactive": "5e0d2aac2ffd3fcefc86e7a9f20a4335f6cdec84", "color.border.default.success": "6241b3b62c526509d24723fbbbfb0f5742520955", "color.border.default.error": "10c5752b42c5a95822d10eb74b71e03ce3c4cccf", "color.border.default.hover": "bb19a151787f28faca2dfed901bbf3eb8dd5b274", "color.text.disabled": "faf10cdadc8e0ece4bca186afe314f1a7b5ae76f", "color.text.success": "4102ed6aee5f71e92d253f35c98e445baffa58f7", "color.text.error": "ac945c2f698d057d2ec6f3bad31a5b552dcd7607", "color.icons.success": "807faf2916ea20a88fc1a001bc1ca1f0bc1d5af4", "color.icons.error": "1e5f96144cc642afeed2a4aa344c17ee29fdd3fa", "color.icons.brand-primary": "1ce0fd21ff86fe5e00f781f15a626253ebbb0862", "color.icons.interactive": "0401d0c8674a0c5ca38843fdcabd130a2f1fc4a2", "color.icons.light": "049180a6d182cd45bca8b4a69eeb00e9666036e1", "color.border.default.warning": "00a6d38105c17453e719856c773bc2f3febf40c9", "color.background.primary-strong": "e59c6659fbaf0ee9f2c120a4f2f940b688142a5c", "color.background.secondary-strong": "71fa46a6e2fb4eb7ba169e29e3058e06726f2dfb", "color.background.tertiary-subtle": "d7efc69a77181b07b35ff1eca0ed1ced0acd0986", "color.background.disabled": "ce6c0697645b418d8d656888606ac8c2b8565aee", "color.hover.primary": "c349f0e12c90338f9040d50b668a9b32aaf3276c", "color.hover.tertiary": "eef5af69935fd957411d636f968a2c6f864210f2", "color.hover.attention": "5ac1504be02e195c11da3238fdb2eec80a19a75d", "color.background.dark-strong": "1392680d15dd3c302c2a71ec2039ae289042118e", "color.background.primary-moderate": "d2cfb283ab716d257e237d86c89b48d2fe983aeb", "color.background.secondary-subtle": "ae7c029bf0e3c11b60466059a39eb482849e6823", "color.background.dark-subtle": "a292a2c3ea0eab7c108fa291790699d0b924b1ca", "color.background.warning-strong": "afa449474d3706f2e3c134bdc19a1e9bfd526e23", "color.background.warning-subtle": "2e6df71237f08ea389a3c5fbdaebe5d6a6357b65", "color.background.success-strong": "79e9714a0d52bdc7c0af5ca924a75de81a36fdc1", "color.background.success-subtle": "26ecc99c8a87c3644159dafa396c85173ebda629", "color.background.failure-strong": "352862c27ef4f71ba77f57e29ec071f6d6e69c4c", "color.background.failure-subtle": "744242d2fd69ff4223679fade42c17b6bc655527", "color.background.attention-strong": "ac7b8357be682117f54386a41d0f5fab54afa493", "color.background.attention-subtle": "cfdc830a33d71dd7397bcc3ddcab2f1b6e2b2c3f", "color.text.warning": "8c38e64643394d06cf1456d172f934940ee401d4", "color.icons.tertiary": "f33dd353bb9dc1e48e6480c2d3c3cbf5c27f8a03", "color.background.neutral-minimal": "ce8c021ea5481de71d20740bb5ac2f7329de2a6f", "color.hover.secondary": "f47df59a98210900cb45f9b69f011bd8c889a17d", "color.background.light": "de29e924479bf8b619ab409426e187fe090137d1", "color.border.default.minimal": "b51cb970b5663ed2cea8390e86e3bf94d44a3a09", "color.border.default.interactive": "61ceea6614d2ab0894100161c7275a673bd0ba63", "color.icons.dark": "4fec5ae68afb2ea3c1c312a68e61b38daebe816f", "color.background.tertiary-strong": "05d5dd81737d95d939591842d5a925eafbe00d4f", "color.icons.minimal": "7f6582aa15306f74fa118e88355edffa41d38a30", "color.icons.secondary": "8d25dab8d222bd8912292b1f7b4b4b4ad010b697", "color.background.dark-moderate": "620be1a3abafee4c0c953090341728c61c137476", "color.background.dark-bold": "d4dc16007d56f103153a0ef5bb1a38512753d958", "color.text.minimal": "52706eb6a9b50a242baf57122888cc904aaef875", "color.border.default.light": "387746e7dba9f28c8d38e5d799e7f4672cd7a95f", "color.border.default.strong": "dcbf6b53cc3af79b049ccc356fc6c174901129df", "color.background.primary-minimal": "02ab621f5c1eb082a7786dd5f52871553b56562e", "color.background.secondary-minimal": "94e83de895715c7527953bc7fac64bfeffd5798b", "color.background.tertiary-minimal": "18cae2ee89e3681acab176d53da14fce3cd08692", "color.background.attention-minimal": "46b7298de8e61cc7b65b953e86721ed917768544", "color.background.warning-minimal": "8594b8321a57f72d602b9658a7728254506cf559", "color.background.success-minimal": "d275335e2bb140a3471f36157969dee2b4bc3819", "color.background.failure-minimal": "f4f09882a48b69f8c58664139dddceb7922587f4", "color.icons.warning": "c2ddefe9b96b4bd699afb0812415f7b7e7ab8c21", "size.spacing.none": "a3a0b08a76e0668f329b4ac0e3da8b6f42072fbf", "size.spacing.2": "439440036f5b76d630cede677a3d1c9c873806d7", "size.spacing.4": "d6c617c75b738748a3347ddb14be4c89f52251e1", "size.spacing.6": "9f246fad69de74c423be941317d9c2559c874261", "size.spacing.8": "926ef6756f9321285329aa33fd23fb0ddfb47cdc", "size.spacing.12": "ac3764ceb2d369e082b66180501996262ed7aa17", "size.spacing.16": "4341f29208c64b4cf39e466c74179e84d3c52bd3", "size.spacing.20": "1f7b7e3e2e7de69f892a48927d9af44250da4745", "size.spacing.24": "22ce4195de314151a4db59e5c7ff28204e08379f", "size.spacing.32": "432c70f5de429485ce2b5841820cd3e781ee5fa1", "size.spacing.40": "edd2551767d33f78f0d7e3a32afb55a0d5230c15", "size.spacing.48": "46b39cfec86d7fffc980fea8f0e97162bb5f01e5", "size.spacing.56": "4bf76485136feb0e4bfdaf366d86f32694803fac", "size.spacing.64": "d5376e5a5327f3fa0021f36c93eb6dfe8d70602e", "size.spacing.80": "51164f126877c48023d939d19f365076cec02d29", "size.spacing.120": "1caacf92a91a5e266f964c86a67dd9b13de2ce9a", "size.border.none": "36037156cf600f2b00e86fc8720ab074c3488461", "size.border.s": "4c26fb4430a552867974fa7d6894f659a0ac1170", "size.border.m": "45cd8d9b257639e7c893313c442b3aaf97f35f1a", "size.corner-radius.square": "51a405a88181625daa989830bfe3c1ed9ea41a22", "size.corner-radius.s": "8e2ecc489ddda461f3edd9c60be4245a06a27eb0", "size.corner-radius.m": "cb5d25bdfbef0d98fcc676fd502f32b5dbd6c003", "size.corner-radius.l": "c4dc835b27c13440937ee4a1e3cf80be6e8199a1", "size.corner-radius.round": "362e5b4133cff7eb7de3f6c75d27e5245bf2cc7b", "color.transparency.attention-subtle": "347243bbb7d1718657a3651910d6d15e75245a4f", "color.transparency.attention-minimal": "2d6aa74744b399677fa4b6810602db798a4bac0c", "color.transparency.secondary-subtle": "b3dcbe1312bec8871b63ad3b1d80eaf1725d389b", "color.transparency.secondary-minimal": "bce1f8e125c6a3817ccf571660b6cdeab399fb96", "color.transparency.dark-subtle": "458c8dd24de4743a8f3c2e57a1e55af7d54be8d0", "color.transparency.dark-bold": "ff6b000c845ca8f3a16b7405cb1ad9cfcaa11b88", "color.background.primary-subtle": "2d74a82a1adef82bffd9b9fec5b9d1c96f92c7cc", "color.background.secondary-moderate": "20efc540ce95e8bd6328f28ab5bde864b45784f2", "color.background.tertiary-moderate": "631b3382fa7b7ed48a325252a3d6296bed069be5", "color.background.attention-moderate": "89df70a74e99a721a3fe43e744fd2687ec5c2e8f", "color.background.warning-moderate": "2998cdabd68745ceac2204ef0f70d998b07df08a", "color.background.success-bold": "e11b4dbe3ab8c37979af6ad89126927988b7a0cb", "color.background.failure-moderate": "8681602703555cffa9415b81bb3ae1650b18c094", "color.background.primary-bold": "c59139f0752b50b927de700369949343268b230c", "color.background.secondary-bold": "651cd8dc0c23568d69882c28ed388133821075a7", "color.background.tertiary-bold": "d236ab352f6093d0504cb2a89f2cf733a5a2e4a3", "color.background.attention-bold": "4d97e03a9707af6c6b587bc31b0cb89b042f227d", "color.background.warning-bold": "aa95a901cd10c7ef8c87df1f82193281fdd66c22", "color.background.success-moderate": "0d28059ecff94cce1bd62fb48337475c1a1b2aff", "color.background.failure-bold": "42bd287cea157ec716d32ed57b557dadcc6a2dd2", "size.spacing.18": "ea4451c399d250264f9112ae98f0d18f8e1855b8", "size.spacing.1": "3baa08f10ba88ab01eab722ad492456fb0143c0e", "color.transparency.primary-minimal": "89c8374e32620af5cf45723ba649858766fe260c", "color.transparency.primary-subtle": "ed036ee43eb43096442017714a601dfdd31d8b4f", "color.hover.neutral": "c0a3f1cfb270557fa5893b2e38c95b0ea53af591", "color.hover.informative": "bb2bd5598f900b882303f3747116a16afbf31a41", "text.display-1.family": "72cf225dcc8c83fa326797ffed3f066ef950c3e9", "text.display-1.moderate-weight": "9d5dd78df373b95fa87d3a9e759ad8d1bd96a888", "text.display-1.size": "128f0c885be69e5e84c2daa6fbf4ae7d6112c14b", "text.display-1.strong-weight": "a6789cea80437e5ffcd9cf870266a0fd6710a4d8", "text.heading-2.family": "66329fcf8171848c801a716f70c3f1284b929e2f", "text.heading-2.strong-weight": "2fa1522484f6cef082dae53611d8c84c21397cb5", "text.heading-2.size": "3b16e1fab9355cab73e0c4329f7ab267f2f0246c", "text.heading-2.moderate-weight": "39ea74b15d22ed856de3a75be20cbbc6357baf92", "text.heading-1.family": "57d0ad686d01ef4a764111f898c2e23a16e78c1b", "text.heading-1.strong-weight": "c799ec002ade0f817dbd38269f6ccc733b34f5ca", "text.heading-1.moderate-weight": "30d73fb8147c1b0f7c99436043610e90f3f08cad", "text.heading-1.size": "5db4c8f470ec803778874cbe4b9c1021372e0fd1", "text.heading-3.family": "b3fb37674de0a39fb6b82efdddaea5cffbe7290a", "text.heading-3.strong-weight": "c4e2c73e3d52c50085444a04714f84ec4d43839f", "text.heading-3.moderate-weight": "66b794b114af3e674249d207b25608c0b4354dea", "text.heading-3.size": "7cf4ef0096547ab1a96f94e0fdf72fc452f79d9d", "text.body-1.family": "c693e01cde163a8f4cbeb6184df4eb56c1de4e05", "text.body-1.strong-weight": "5e3e9db7875373c9d713c0f02b236423caadc7d7", "text.body-1.size": "11d241478d18e8df47a0848377ec613b1a61473e", "text.body-1.moderate-weight": "db0d96db4853b2daf96b5abb303c03f0492f2b4e", "text.body-3.family": "512173edaa906c26c0d5708b72f89434fd732167", "text.body-3.strong-weight": "337c538bc7227345ce20e60bf08ce2689a777337", "text.body-3.moderate-weight": "9160cb25c57a9b4c69f8b7d74250cd47d936020d", "text.body-3.size": "841c7ffc50847294367e8b95ef0673c7a67a00bd", "text.body-2.family": "3249ac4cdf913f75b5db20f0671721296c3f502b", "text.body-2.strong-weight": "aac7344a81352e151e5830354bb202ba1b5bbb4f", "text.body-2.size": "f898df75b03708ef3512db0155c91b0703b9975b", "text.body-2.moderate-weight": "0c556e3027a5d73b4f5dfbe71bf480e6c5a804e6", "text.caption-1.family": "ae109d080a050af76494e6ea16656a30e9417800", "text.caption-1.size": "a537af8affaeff864b511e906b1ef6a141daff99", "text.caption-1.moderate-weight": "ee594618ddadedbbabda1ed24c591ce3cee07d16", "text.display-1.line-height": "fa94251ab6dc40a1cc97ed9cc1e99604aed2b891", "text.heading-1.line-height": "c3cfacd3f5476898bc002461d8a77cdc48709cd4", "text.heading-2.line-height": "1da619b9d0507c0b1b5489321da01a287cde362b", "text.heading-3.line-height": "1d8449fb4b43ce102f591024657b495fbd25398d", "text.body-1.line-height": "5a1283b8b94d710fe78430083d7bfc2d9b115f4d", "text.body-2.line-height": "79870c89a34f131236b785be450ad3cdd1c9bcdf", "text.body-3.line-height": "f4d272292a3cf17e29e6c5572cf87e63d8e05bda", "text.caption-1.line-height": "49f23923cc145ac329b086a49b4c2c7dc1c6b135", "color.border.company-graph.ubo": "7579cb5a9e70063c539aa15b5cff78a63f95c951", "color.border.company-graph.company": "7c448c763d4d444e8f6fc0f70839a8d61f44f491", "color.border.company-graph.person": "d32c513899756ced1fb997d96db811277ab79238", "color.elevation.small": "60b84d4c0f41286174a494c81acc4fdf9322f602", "color.elevation.medium": "19056134f25e1972547f5cce1069b9f8467697d0", "color.elevation.large": "7266efb5840a78d4d56713121aee4fe35ad42abd", "color.transparency.neutral-moderate": "957c359179047ed067ed61608f9d596023fae6e3", "color.background.dark-minimal": "e9f656a7b50472b5b24bedc8df76519869621c44"}, "$figmaModeId": "38543:0", "$figmaCollectionId": "VariableCollectionId:5120:13788", "group": "2-<PERSON><PERSON>"}, {"id": "3-component-neoshare", "name": "neoshare", "group": "3-Component", "selectedTokenSets": {"3-Component/neoshare": "enabled"}, "$figmaStyleReferences": {}, "$figmaVariableReferences": {"buttons.main-set.primary.color.background.default": "0d3c773813279ef2bef9038ee106cbc5f00f7bd7", "illustrations.business-type.color.primary": "3eb9384d4837a7d62f36d2ff7a7c564bfb6b5253", "buttons.main-set.primary.color.background.hover": "e51e22702c756b4682fe51406b504056946adbcd", "buttons.main-set.primary.color.background.disabled": "fd0d9d040c16a2057f68eb5458f7f9ae302aa868", "buttons.main-set.primary.color.background.attention": "f957a172a5d754856408af22850682ef67efe4a2", "buttons.main-set.primary.color.background.attention-hover": "2196e9b3227f2de008d064e4da45502eec41aa9a", "buttons.main-set.primary.color.label.default": "a6b4196d4779f473926ab354d097b8050bdabf45", "buttons.main-set.primary.color.label.hover": "ec6df9e67c690d6e510d594f5df439742c8116f7", "buttons.main-set.primary.color.label.disabled": "8e89d7c7c1487775764aa0a879913d43de3a8f2c", "buttons.main-set.primary.color.label.attention": "6a49f4897bc423370839e5b47512b94ce6a4f3ef", "buttons.main-set.primary.color.label.attention-hover": "363613bf980dcfbf99e240d4c7ca343bb01b3a42", "buttons.main-set.secondary.color.background.attention-hover": "72281067bacc1d75051c1de3b366dfafa4022bd5", "buttons.main-set.secondary.color.background.hover": "0e2826c992cbc72442150a4c95df160f98ae8943", "buttons.main-set.secondary.color.border.attention-hover": "89645cffdd3253d31c4ad189e97ed60890eb531a", "buttons.main-set.secondary.color.border.attention": "1b60d08725027a18ef60133e7fb4312420d1d2b1", "buttons.main-set.secondary.color.border.disabled": "4dcb660451c577e4cf4b999c644bee3b15b166f4", "buttons.main-set.secondary.color.border.hover": "c697eb9e8a7c9d47c2453afcb627a21fd635cfc4", "buttons.main-set.secondary.color.border.default": "54313c38009de21e4e158d47e7c6e15583b86074", "buttons.main-set.secondary.color.label.attention-hover": "3790bfb900d38b5667d082ccaaa0551adeecff44", "buttons.main-set.secondary.color.label.attention": "a5fc04e657b4e07edd1ea04a2678d84c196326b7", "buttons.main-set.secondary.color.label.disabled": "5b4b08f8d4f2d9bd5b5ca63269fff1f9b0ac763f", "buttons.main-set.secondary.color.label.hover": "91771be8edf13973331d7383269b9f08ba5d7e1d", "buttons.main-set.secondary.color.label.default": "9d3db32c01ed611dafc70d6d976c9918603be9d9", "buttons.main-set.informative.color.background.default": "602a1000d1cf485952a7863a0c607cc42bb4e503", "buttons.main-set.informative.color.background.hover": "d53a92336ba6f0af47f367b53d58f5393eb95d5e", "buttons.main-set.informative.color.background.disabled": "ce7e98fe0e79e0da264608715e2f24c70f2ac7ea", "buttons.main-set.informative.color.background.attention": "b318882e3b3fecb4045149418aff77c68e3e53d4", "buttons.main-set.informative.color.background.attention-hover": "a5ba5142081ad1268edbd95264492133674b3c99", "buttons.main-set.informative.color.label.default": "71bb422e6cc526a1d5ac606983d2ae3c19410146", "buttons.main-set.informative.color.label.hover": "bbc78c45330bdc3481d0e5f5e54a8791164d72c1", "buttons.main-set.informative.color.label.disabled": "881f271525460f9ccd8dd17ec546c1bed8f8629d", "buttons.main-set.informative.color.label.attention": "9e0921d89a6029a2316f2810a2c6943b38b84403", "buttons.main-set.informative.color.label.attention-hover": "3489deb36c104a039b7b532f20211adad5a9e7ba", "buttons.main-set.stealth.color.background.hover": "03293fca2e3f668d644bfdbc2a1831db7e3fa9ab", "buttons.main-set.stealth.color.background.attention-hover": "59c27a0922b06d5f366e9c6418bb51b33a222f7c", "buttons.main-set.stealth.color.label.default": "fa0bad2a89768ea3176ec9a85a5717d682c9ac36", "buttons.main-set.stealth.color.label.hover": "ea51541daa7e5f81d7858c8e57df514780fadbc2", "buttons.main-set.stealth.color.label.disabled": "3357cf13b780b9247f2912259eeb10c84042f90a", "buttons.main-set.stealth.color.label.attention": "093d7d71e44fc24dede6bfc06f3c0e0960777ba7", "buttons.main-set.stealth.color.label.attention-hover": "ac6e436c8c8050c3c0e042ebe53de4ee80f6f37f", "buttons.icon-only.primary.color.background.default": "66cf8a49f94f6ee382e58a4d064104736e4f59f1", "buttons.icon-only.primary.color.background.hover": "94a3031105d5fa27bedcc86d1604464307a49cd4", "buttons.icon-only.primary.color.background.disabled": "35cba3d9ec05d34efdf19282701a8baec5d50450", "buttons.icon-only.primary.color.background.attention": "92a2d39a05cef072d818a2ce031a75f4a9b83cd3", "buttons.icon-only.primary.color.background.attention-hover": "e3ba0ec5e48d2b49af3c34b06217716270149582", "buttons.icon-only.primary.color.icon.default": "6bfe0e4066947d5ca0a4b13733c729855ffaf1d8", "buttons.icon-only.primary.color.icon.hover": "92873b38f78ee438ec284661a8af6c402951f762", "buttons.icon-only.primary.color.icon.disabled": "1ffaeacb2b9b3161e561d9b2c3fec86f805e3f50", "buttons.icon-only.primary.color.icon.attention": "4ead26e4dbb24ec64bb0d29a7b33afcba3bc754f", "buttons.icon-only.primary.color.icon.attention-hover": "041a46e2d53ad2843b006686fc66dad7e3772ee5", "buttons.icon-only.informative.color.background.default": "8c150fb4f3309a4a5d975f155582435c18e17a1e", "buttons.icon-only.informative.color.background.hover": "715836be0e039546ded312125546606936580b57", "buttons.icon-only.informative.color.background.disabled": "44ed2fab389e7afafb84eec4c58543677b7b8939", "buttons.icon-only.informative.color.background.attention": "6c336cc7c946725b8b2e98590c35118f52754f30", "buttons.icon-only.informative.color.background.attention-hover": "600c1857a565032df81b11b8439e16976c48a131", "buttons.icon-only.informative.color.icon.default": "ce17069850fd749bd89a0e9e250ae0473592809d", "buttons.icon-only.informative.color.icon.hover": "cd2efabce64ba433f77ed7e83337bda6244ec7d7", "buttons.icon-only.informative.color.icon.disabled": "473cac7ee956ac6d27de1b7344dab2cdc5445812", "buttons.icon-only.informative.color.icon.attention": "6aff4d65194300adc18d6f457c5f9c839da6d938", "buttons.icon-only.informative.color.icon.attention-hover": "fbd16b54a6986aff803999442c74e7ea8def14aa", "buttons.main-set.primary.color.icon.default": "61d997b4c593629d3d548d3f5446165e47f36f4c", "buttons.main-set.primary.color.icon.hover": "7fe48e90665eb7e966bc40c9fe4ec87efdccd62c", "buttons.main-set.primary.color.icon.disabled": "5fdada702fe7b0f99560980f918a40609f6b06a4", "buttons.main-set.primary.color.icon.attention": "3ac508c76ed8c6db1da33206f909989742550082", "buttons.main-set.primary.color.icon.attention-hover": "5e697a195c2c05670651efa7852c5672c387a0ab", "buttons.main-set.secondary.color.icon.default": "b1bfff5e10b29285166ebc40751fcbdf0286d0b6", "buttons.main-set.secondary.color.icon.hover": "4a1a1851501db4a2f9591706513b784423eeb2a1", "buttons.main-set.secondary.color.icon.disabled": "2b60c2d00ec9a2c1830c99c99e1811d6ed33545d", "buttons.main-set.secondary.color.icon.attention": "8bf79dd98f57f8b09dfeb3027efe67067c15c432", "buttons.main-set.secondary.color.icon.attention-hover": "9289a2ffed6e28daf4a5456ca051e3489f681e60", "buttons.main-set.informative.color.icon.default": "d62880d9dd2be75da6d34c1493465d31e558aec1", "buttons.main-set.informative.color.icon.hover": "879fe098934952543a3571aa9ef32393f6c46c55", "buttons.main-set.informative.color.icon.disabled": "08ea8b0eceb885ebd78c73f5829f8c8e910ea766", "buttons.main-set.informative.color.icon.attention": "1a145f52d62cd39055cdcccfb63cacfbac0ec513", "buttons.main-set.informative.color.icon.attention-hover": "9c4aafe7c75968ae23df49331ac599f5e64dabe1", "buttons.main-set.stealth.color.icon.default": "31a8c054d4bb7bacaa9f7e364b708b3f49693233", "buttons.main-set.stealth.color.icon.hover": "b87dc242a3f711b460f18a5c6cd5ff18d7c1b42f", "buttons.main-set.stealth.color.icon.disabled": "7d17b6ce461c0061f6062abdecb234edf45adff9", "buttons.main-set.stealth.color.icon.attention": "407a7c31c41b7307705555e04be47d15235b69b5", "buttons.main-set.stealth.color.icon.attention-hover": "ed7b278d3230c563597ac92ebf3dc422a0b9afdb", "buttons.icon-only.secondary.color.background.hover": "739bf8b0c1910653f403a520e79b91d1d8439a3f", "buttons.icon-only.secondary.color.background.attention-hover": "457d24fc8b47961844e0892c6d42fe84d4a71cd7", "buttons.icon-only.secondary.color.border.default": "f3ce1699b2cb2a28d735fd28cb06e6e3e41d2aee", "buttons.icon-only.secondary.color.border.hover": "f714c31a1c0124187f6acdec4d207932f467d3ad", "buttons.icon-only.secondary.color.border.disabled": "81e3fb7c90d8c1ff08c3df543de0fa64403bbe2c", "buttons.icon-only.secondary.color.border.attention": "83084fc351532e62ffe8cb4efb20224db027aadb", "buttons.icon-only.secondary.color.border.attention-hover": "a93871c8e46358af2dc356da20900964f773e48a", "buttons.icon-only.secondary.color.icon.default": "3ade2e6f9a641b8ade46e00bf9ea6700ec264203", "buttons.icon-only.secondary.color.icon.hover": "35d43617761a4e91b42474f68b1a98cc6c5bcb91", "buttons.icon-only.secondary.color.icon.disabled": "f69efb95ae277023298a967f4fabe85a3e2cfd48", "buttons.icon-only.secondary.color.icon.attention": "c5efc9e3f1c84f16c58083e00f8ddc689ffabc93", "buttons.icon-only.secondary.color.icon.attention-hover": "13050af2d58bc00608508949ab3dce93c0ed5405", "buttons.icon-only.stealth.color.background.hover": "4f3958b29bd657136b7ea55ff9ff1b024f472a6c", "buttons.icon-only.stealth.color.background.attention-hover": "1b0da33a0794793c5f40f1a7f0db159dc8de4a9c", "buttons.icon-only.stealth.color.icon.default": "fbdd7be4c8d2a2472fdaabfa02f153f22249174e", "buttons.icon-only.stealth.color.icon.hover": "cf113600f58b563d4b83e3e5f183863d75b0dbbb", "buttons.icon-only.stealth.color.icon.disabled": "7e0c30b2ab59d27f2c94744e8f081fe5843569ff", "buttons.icon-only.stealth.color.icon.attention": "021c4f68fb82e816aed018f5a028bd9a291abe0f", "buttons.icon-only.stealth.color.icon.attention-hover": "5095c8ed7c6401a8b24d737e60e4a7b49f8da51d", "buttons.fab.color.background.default": "0901c5ae2818f392cb70079010b92bc211672c1f", "buttons.fab.color.background.hover": "0ae88f2c4a4b70b9e51b54126d3756d079f9f09a", "buttons.fab.color.icon.default": "66199232e7695b2023ae8964687b7d35a96e0a81", "buttons.fab.color.icon.hover": "258149e0043066871a179d9c14379554838e33fd", "buttons.split.primary.color.background.default": "b7e486d0b0a92711f645e4481b0e55cd9e98e908", "buttons.split.primary.color.background.hover": "9c1719f811fc5ae427b459575bb0bd8e8f20d4f3", "buttons.split.primary.color.background.disabled": "161a33a1c049fbf5a8a1d389f9b521d32c6b97d6", "buttons.split.primary.color.background.attention": "c9df8882251059668fa3d38806fa0ccbf217d653", "buttons.split.primary.color.background.attention-hover": "f863b5081d58e072478d9bd169f28da68a3250a7", "buttons.split.primary.color.label.default": "dbfd09cca93c3a2a2ac67da108b1ee1184686fec", "buttons.split.primary.color.label.hover": "da8da475edff88773f3c6bc4d79a56006cc1296a", "buttons.split.primary.color.label.disabled": "e3f62e35dc73111f3ce2d66d3be28d85aaa02362", "buttons.split.primary.color.label.attention": "2afca8ec39247abd9048626da602d6df31bd877d", "buttons.split.primary.color.label.attention-hover": "e85b42f3c6691d0e86869d4c57831e090c606363", "buttons.split.primary.color.icon.default": "aa7dd8c927b9c6f240e81a5dbd3abcf3403d853b", "buttons.split.primary.color.icon.hover": "31aefa4f59805a586a5b2b31c30a680c49f7514f", "buttons.split.primary.color.icon.disabled": "4a4d6cf830592f79a1fc77c01729b359e90cf370", "buttons.split.primary.color.icon.attention": "d44602622917f35d89c5658c6e5c613f57f9051a", "buttons.split.primary.color.icon.attention-hover": "06b494a7689e7b10352909ad6d89dde48350a81c", "buttons.split.primary.color.separator.default": "7f5a5dced7557e1b2ed9e2cad9757e066566c5d7", "buttons.split.primary.color.separator.hover": "98d0d4aeefe164bc648dfad2c6897953bd08875e", "buttons.split.primary.color.separator.disabled": "1b33d34c08a64a0887e9c6e83ef6871d1b1cd362", "buttons.split.primary.color.separator.attention": "b22819be0c486ee4a019ca9f11139e2587c9e3e7", "buttons.split.primary.color.separator.attention-hover": "d2c4ce811a5ca5cb304700e605f0acedb44245ff", "buttons.split.secondary.color.background.hover": "e7d26ebc24d3902b3865992a9ca40318aacdfee4", "buttons.split.secondary.color.background.attention-hover": "9aed6293b6464f378aed777091ff568e0835033e", "buttons.split.secondary.color.border.default": "bb936b92b35b34b76e51cdee5ce790a5145bce0f", "buttons.split.secondary.color.border.hover": "df9237658edfdf597765bdf1f9bd5431303418db", "buttons.split.secondary.color.border.disabled": "8961c7ee2603b84d121f580809d0964685107fea", "buttons.split.secondary.color.border.attention": "b09fed11ac333427b0e15475ec961f6d5a7d6f8d", "buttons.split.secondary.color.border.attention-hover": "d1e6e32e40017030410e20d333d6585d00dc062c", "buttons.split.secondary.color.label.default": "a3c0927b19ba19fe1263a4ba336d36ecfbae253d", "buttons.split.secondary.color.label.hover": "1e21bc49d438ce0f61c9ce275023544b2ac73cdf", "buttons.split.secondary.color.label.disabled": "b5fe6e57b40da44a32b2dbe765ec95cc6a8b826b", "buttons.split.secondary.color.label.attention": "208ff6eb6ae2d3f6eb0aaf70f32abfb8944f84b7", "buttons.split.secondary.color.label.attention-hover": "f0501253a157187f18bd7d26abf63f0c8e2d547f", "buttons.split.secondary.color.icon.default": "09b86d6d44465b2efd237c7bbd1fad2e1e0397f6", "buttons.split.secondary.color.icon.hover": "28d66664e6ef770e78ddd5491c75cd159b879069", "buttons.split.secondary.color.icon.disabled": "262b19cbd0252fcd9502a81d4d36b23e277555aa", "buttons.split.secondary.color.icon.attention": "29a2834d73421029d966a92cad58a3c19c9f461c", "buttons.split.secondary.color.icon.attention-hover": "004a053d8bce618a900fe071a0c720295c700fca", "buttons.split.secondary.color.separator.default": "958ea51f7a6636373eb27c83151503b320a1c581", "buttons.split.secondary.color.separator.hover": "39ca547576d51b8150c6287c7db051f6dada1a3d", "buttons.split.secondary.color.separator.disabled": "d88df8dc64c7cf0b63564ce3988e36feade95337", "buttons.split.secondary.color.separator.attention": "f8c713515f5cf3cd2e402ec6d7f502d0d02ed86f", "buttons.split.secondary.color.separator.attention-hover": "b40d7e83df4626d4a70dad8012dd0f5bcca0d0c4", "buttons.split.informative.color.background.default": "02623a101b7103c56b46f5dde6cf6e5cd5245e77", "buttons.split.informative.color.background.hover": "d6c55eef761c48a9e1c16e75ad084613033c7f4d", "buttons.split.informative.color.background.disabled": "1b8ea92d953f0f7d2a8f998ed57e781c06629bf1", "buttons.split.informative.color.background.attention": "addf5439487f7f6108587e3d6a7935850cb91f23", "buttons.split.informative.color.background.attention-hover": "650ead828894518c90237beabe6abb63dbdd46f2", "buttons.split.informative.color.label.default": "255006e8389a9f711b34816af7630e5c736430eb", "buttons.split.informative.color.label.hover": "23c1f552199c7cbf6f1b10ec95428e64e0120e98", "buttons.split.informative.color.label.disabled": "bbd0f7a5f373399cff8ea839c0d939e84a1e9f2d", "buttons.split.informative.color.label.attention": "25fc55304f9e4af5ef1d07b8aec670b812f20e38", "buttons.split.informative.color.label.attention-hover": "61afaae9c71d77504d70e053eeeca867e27c078d", "buttons.split.informative.color.icon.default": "0a865ffe54fb622da81288c5ac5b73d448c8407b", "buttons.split.informative.color.icon.hover": "b5bc44993d9a39b6a8eb8e8bf67b2101c6570e5b", "buttons.split.informative.color.icon.disabled": "a41905d8b7d5a503d1cf6b4784c691d950eb755b", "buttons.split.informative.color.icon.attention": "114269f9d374934fd822dc8368d4f53aef14b1ea", "buttons.split.informative.color.icon.attention-hover": "e30d49bc528c55d3f259ea01995f2367f17729c7", "buttons.split.informative.color.separator.default": "83119baba4c653ac105000d1cb660e069716685b", "buttons.split.informative.color.separator.hover": "daebb9fdd7b362a594cbc4735373a05ac476f6c5", "buttons.split.informative.color.separator.disabled": "7e945709d9ba0f1094271dceace05914bbec4c9f", "buttons.split.informative.color.separator.attention": "6df3f4d46f506538ca9fc80dc8329d19fbf69120", "buttons.split.informative.color.separator.attention-hover": "48ccd7a48270e8205021c9542657d7a66dd9d8ed", "buttons.action.primary.color.background.hover": "a05b1ad5759521cec75a2cc4f6b9f49f2d2098cc", "buttons.action.primary.color.icon.default": "8639a0382492df9667dce6c3c17d07541e109ffa", "buttons.action.primary.color.icon.hover": "e867c4a5b83ee9fb0d119d04e85b6ba1e99060f3", "buttons.action.primary.color.icon.disabled": "4e0d0bd29dcb2a44e2c9fccee908dd350ff9de0f", "buttons.action.primary.color.icon.attention": "036235a15d5903b2146cd21a8928ad1247d1f6a9", "buttons.action.primary.color.icon.attention-hover": "3045e7b2d45286f47ea38fc47be0bcb745b2f8a7", "buttons.action.informative.color.background.hover": "e0f7179e77685726db0b736042bd5a436d3560a8", "buttons.action.informative.color.icon.default": "7f44fbed8939243f448e1538963f0e30cc566278", "buttons.action.informative.color.icon.hover": "c5706881232effeca0c9c4a41d42e2082314df9f", "buttons.action.informative.color.icon.disabled": "367d232565ced6951bba69fd74cad82f0b04b296", "buttons.action.informative.color.icon.attention": "e0d2ca923d5b1badb02067d0ce9e1fb646d982ad", "buttons.action.informative.color.icon.attention-hover": "27cf7410bb7553d8eaf480ebf57f48edad187af4", "buttons.action.primary.color.background.attention-hover": "3c528f677ca6c3c4422484484663c7a64b27ed82", "buttons.action.informative.color.background.attention-hover": "59213e48f855aa396f48fce6fd437f96b5dc8c91", "buttons.action.tertiary.color.background.hover": "7e48a5ac5129b8f4fa3b865e8f1072042a6515f2", "buttons.action.tertiary.color.background.attention-hover": "9abff638d670a18b9c5d3270cb333e7bfdc220f8", "buttons.action.tertiary.color.icon.default": "779b7f116c84367b38034582b855dd9a7a20c457", "buttons.action.tertiary.color.icon.hover": "7e4ba87ad04f87b17794e3818c7b3849a2e97740", "buttons.action.tertiary.color.icon.disabled": "61722d78768de5760f61d0a1ee3ce626354465ef", "buttons.action.tertiary.color.icon.attention": "2698d10ab3b9d9f7e0338662b8fe4c06fb4e3e53", "buttons.action.tertiary.color.icon.attention-hover": "2b49d00b4ba463fbc045b38b428911e7f60bce2d", "illustrations.business-type.color.secondary": "2d29e782562d3ca67faadce3a99d5f49bb72d116", "illustrations.business-type.color.light": "8a293d19ca5c4b5dd06a44e7e865ed0d889efd2d"}, "$figmaModeId": "5120:3", "$figmaCollectionId": "VariableCollectionId:5120:13789"}, {"id": "3-component-volksbank", "name": "volksbank", "group": "3-Component", "selectedTokenSets": {"3-Component/volksbank": "enabled"}, "$figmaStyleReferences": {}, "$figmaVariableReferences": {"buttons.main-set.primary.color.background.default": "0d3c773813279ef2bef9038ee106cbc5f00f7bd7", "illustrations.business-type.color.primary": "3eb9384d4837a7d62f36d2ff7a7c564bfb6b5253", "buttons.main-set.primary.color.background.hover": "e51e22702c756b4682fe51406b504056946adbcd", "buttons.main-set.primary.color.background.disabled": "fd0d9d040c16a2057f68eb5458f7f9ae302aa868", "buttons.main-set.primary.color.background.attention": "f957a172a5d754856408af22850682ef67efe4a2", "buttons.main-set.primary.color.background.attention-hover": "2196e9b3227f2de008d064e4da45502eec41aa9a", "buttons.main-set.primary.color.label.default": "a6b4196d4779f473926ab354d097b8050bdabf45", "buttons.main-set.primary.color.label.hover": "ec6df9e67c690d6e510d594f5df439742c8116f7", "buttons.main-set.primary.color.label.disabled": "8e89d7c7c1487775764aa0a879913d43de3a8f2c", "buttons.main-set.primary.color.label.attention": "6a49f4897bc423370839e5b47512b94ce6a4f3ef", "buttons.main-set.primary.color.label.attention-hover": "363613bf980dcfbf99e240d4c7ca343bb01b3a42", "buttons.main-set.secondary.color.background.attention-hover": "72281067bacc1d75051c1de3b366dfafa4022bd5", "buttons.main-set.secondary.color.background.hover": "0e2826c992cbc72442150a4c95df160f98ae8943", "buttons.main-set.secondary.color.border.attention-hover": "89645cffdd3253d31c4ad189e97ed60890eb531a", "buttons.main-set.secondary.color.border.attention": "1b60d08725027a18ef60133e7fb4312420d1d2b1", "buttons.main-set.secondary.color.border.disabled": "4dcb660451c577e4cf4b999c644bee3b15b166f4", "buttons.main-set.secondary.color.border.hover": "c697eb9e8a7c9d47c2453afcb627a21fd635cfc4", "buttons.main-set.secondary.color.border.default": "54313c38009de21e4e158d47e7c6e15583b86074", "buttons.main-set.secondary.color.label.attention-hover": "3790bfb900d38b5667d082ccaaa0551adeecff44", "buttons.main-set.secondary.color.label.attention": "a5fc04e657b4e07edd1ea04a2678d84c196326b7", "buttons.main-set.secondary.color.label.disabled": "5b4b08f8d4f2d9bd5b5ca63269fff1f9b0ac763f", "buttons.main-set.secondary.color.label.hover": "91771be8edf13973331d7383269b9f08ba5d7e1d", "buttons.main-set.secondary.color.label.default": "9d3db32c01ed611dafc70d6d976c9918603be9d9", "buttons.main-set.informative.color.background.default": "602a1000d1cf485952a7863a0c607cc42bb4e503", "buttons.main-set.informative.color.background.hover": "d53a92336ba6f0af47f367b53d58f5393eb95d5e", "buttons.main-set.informative.color.background.disabled": "ce7e98fe0e79e0da264608715e2f24c70f2ac7ea", "buttons.main-set.informative.color.background.attention": "b318882e3b3fecb4045149418aff77c68e3e53d4", "buttons.main-set.informative.color.background.attention-hover": "a5ba5142081ad1268edbd95264492133674b3c99", "buttons.main-set.informative.color.label.default": "71bb422e6cc526a1d5ac606983d2ae3c19410146", "buttons.main-set.informative.color.label.hover": "bbc78c45330bdc3481d0e5f5e54a8791164d72c1", "buttons.main-set.informative.color.label.disabled": "881f271525460f9ccd8dd17ec546c1bed8f8629d", "buttons.main-set.informative.color.label.attention": "9e0921d89a6029a2316f2810a2c6943b38b84403", "buttons.main-set.informative.color.label.attention-hover": "3489deb36c104a039b7b532f20211adad5a9e7ba", "buttons.main-set.stealth.color.background.hover": "03293fca2e3f668d644bfdbc2a1831db7e3fa9ab", "buttons.main-set.stealth.color.background.attention-hover": "59c27a0922b06d5f366e9c6418bb51b33a222f7c", "buttons.main-set.stealth.color.label.default": "fa0bad2a89768ea3176ec9a85a5717d682c9ac36", "buttons.main-set.stealth.color.label.hover": "ea51541daa7e5f81d7858c8e57df514780fadbc2", "buttons.main-set.stealth.color.label.disabled": "3357cf13b780b9247f2912259eeb10c84042f90a", "buttons.main-set.stealth.color.label.attention": "093d7d71e44fc24dede6bfc06f3c0e0960777ba7", "buttons.main-set.stealth.color.label.attention-hover": "ac6e436c8c8050c3c0e042ebe53de4ee80f6f37f", "buttons.icon-only.primary.color.background.default": "66cf8a49f94f6ee382e58a4d064104736e4f59f1", "buttons.icon-only.primary.color.background.hover": "94a3031105d5fa27bedcc86d1604464307a49cd4", "buttons.icon-only.primary.color.background.disabled": "35cba3d9ec05d34efdf19282701a8baec5d50450", "buttons.icon-only.primary.color.background.attention": "92a2d39a05cef072d818a2ce031a75f4a9b83cd3", "buttons.icon-only.primary.color.background.attention-hover": "e3ba0ec5e48d2b49af3c34b06217716270149582", "buttons.icon-only.primary.color.icon.default": "6bfe0e4066947d5ca0a4b13733c729855ffaf1d8", "buttons.icon-only.primary.color.icon.hover": "92873b38f78ee438ec284661a8af6c402951f762", "buttons.icon-only.primary.color.icon.disabled": "1ffaeacb2b9b3161e561d9b2c3fec86f805e3f50", "buttons.icon-only.primary.color.icon.attention": "4ead26e4dbb24ec64bb0d29a7b33afcba3bc754f", "buttons.icon-only.primary.color.icon.attention-hover": "041a46e2d53ad2843b006686fc66dad7e3772ee5", "buttons.icon-only.informative.color.background.default": "8c150fb4f3309a4a5d975f155582435c18e17a1e", "buttons.icon-only.informative.color.background.hover": "715836be0e039546ded312125546606936580b57", "buttons.icon-only.informative.color.background.disabled": "44ed2fab389e7afafb84eec4c58543677b7b8939", "buttons.icon-only.informative.color.background.attention": "6c336cc7c946725b8b2e98590c35118f52754f30", "buttons.icon-only.informative.color.background.attention-hover": "600c1857a565032df81b11b8439e16976c48a131", "buttons.icon-only.informative.color.icon.default": "ce17069850fd749bd89a0e9e250ae0473592809d", "buttons.icon-only.informative.color.icon.hover": "cd2efabce64ba433f77ed7e83337bda6244ec7d7", "buttons.icon-only.informative.color.icon.disabled": "473cac7ee956ac6d27de1b7344dab2cdc5445812", "buttons.icon-only.informative.color.icon.attention": "6aff4d65194300adc18d6f457c5f9c839da6d938", "buttons.icon-only.informative.color.icon.attention-hover": "fbd16b54a6986aff803999442c74e7ea8def14aa", "buttons.main-set.primary.color.icon.default": "61d997b4c593629d3d548d3f5446165e47f36f4c", "buttons.main-set.primary.color.icon.hover": "7fe48e90665eb7e966bc40c9fe4ec87efdccd62c", "buttons.main-set.primary.color.icon.disabled": "5fdada702fe7b0f99560980f918a40609f6b06a4", "buttons.main-set.primary.color.icon.attention": "3ac508c76ed8c6db1da33206f909989742550082", "buttons.main-set.primary.color.icon.attention-hover": "5e697a195c2c05670651efa7852c5672c387a0ab", "buttons.main-set.secondary.color.icon.default": "b1bfff5e10b29285166ebc40751fcbdf0286d0b6", "buttons.main-set.secondary.color.icon.hover": "4a1a1851501db4a2f9591706513b784423eeb2a1", "buttons.main-set.secondary.color.icon.disabled": "2b60c2d00ec9a2c1830c99c99e1811d6ed33545d", "buttons.main-set.secondary.color.icon.attention": "8bf79dd98f57f8b09dfeb3027efe67067c15c432", "buttons.main-set.secondary.color.icon.attention-hover": "9289a2ffed6e28daf4a5456ca051e3489f681e60", "buttons.main-set.informative.color.icon.default": "d62880d9dd2be75da6d34c1493465d31e558aec1", "buttons.main-set.informative.color.icon.hover": "879fe098934952543a3571aa9ef32393f6c46c55", "buttons.main-set.informative.color.icon.disabled": "08ea8b0eceb885ebd78c73f5829f8c8e910ea766", "buttons.main-set.informative.color.icon.attention": "1a145f52d62cd39055cdcccfb63cacfbac0ec513", "buttons.main-set.informative.color.icon.attention-hover": "9c4aafe7c75968ae23df49331ac599f5e64dabe1", "buttons.main-set.stealth.color.icon.default": "31a8c054d4bb7bacaa9f7e364b708b3f49693233", "buttons.main-set.stealth.color.icon.hover": "b87dc242a3f711b460f18a5c6cd5ff18d7c1b42f", "buttons.main-set.stealth.color.icon.disabled": "7d17b6ce461c0061f6062abdecb234edf45adff9", "buttons.main-set.stealth.color.icon.attention": "407a7c31c41b7307705555e04be47d15235b69b5", "buttons.main-set.stealth.color.icon.attention-hover": "ed7b278d3230c563597ac92ebf3dc422a0b9afdb", "buttons.icon-only.secondary.color.background.hover": "739bf8b0c1910653f403a520e79b91d1d8439a3f", "buttons.icon-only.secondary.color.background.attention-hover": "457d24fc8b47961844e0892c6d42fe84d4a71cd7", "buttons.icon-only.secondary.color.border.default": "f3ce1699b2cb2a28d735fd28cb06e6e3e41d2aee", "buttons.icon-only.secondary.color.border.hover": "f714c31a1c0124187f6acdec4d207932f467d3ad", "buttons.icon-only.secondary.color.border.disabled": "81e3fb7c90d8c1ff08c3df543de0fa64403bbe2c", "buttons.icon-only.secondary.color.border.attention": "83084fc351532e62ffe8cb4efb20224db027aadb", "buttons.icon-only.secondary.color.border.attention-hover": "a93871c8e46358af2dc356da20900964f773e48a", "buttons.icon-only.secondary.color.icon.default": "3ade2e6f9a641b8ade46e00bf9ea6700ec264203", "buttons.icon-only.secondary.color.icon.hover": "35d43617761a4e91b42474f68b1a98cc6c5bcb91", "buttons.icon-only.secondary.color.icon.disabled": "f69efb95ae277023298a967f4fabe85a3e2cfd48", "buttons.icon-only.secondary.color.icon.attention": "c5efc9e3f1c84f16c58083e00f8ddc689ffabc93", "buttons.icon-only.secondary.color.icon.attention-hover": "13050af2d58bc00608508949ab3dce93c0ed5405", "buttons.icon-only.stealth.color.background.hover": "4f3958b29bd657136b7ea55ff9ff1b024f472a6c", "buttons.icon-only.stealth.color.background.attention-hover": "1b0da33a0794793c5f40f1a7f0db159dc8de4a9c", "buttons.icon-only.stealth.color.icon.default": "fbdd7be4c8d2a2472fdaabfa02f153f22249174e", "buttons.icon-only.stealth.color.icon.hover": "cf113600f58b563d4b83e3e5f183863d75b0dbbb", "buttons.icon-only.stealth.color.icon.disabled": "7e0c30b2ab59d27f2c94744e8f081fe5843569ff", "buttons.icon-only.stealth.color.icon.attention": "021c4f68fb82e816aed018f5a028bd9a291abe0f", "buttons.icon-only.stealth.color.icon.attention-hover": "5095c8ed7c6401a8b24d737e60e4a7b49f8da51d", "buttons.fab.color.background.default": "0901c5ae2818f392cb70079010b92bc211672c1f", "buttons.fab.color.background.hover": "0ae88f2c4a4b70b9e51b54126d3756d079f9f09a", "buttons.fab.color.icon.default": "66199232e7695b2023ae8964687b7d35a96e0a81", "buttons.fab.color.icon.hover": "258149e0043066871a179d9c14379554838e33fd", "buttons.split.primary.color.background.default": "b7e486d0b0a92711f645e4481b0e55cd9e98e908", "buttons.split.primary.color.background.hover": "9c1719f811fc5ae427b459575bb0bd8e8f20d4f3", "buttons.split.primary.color.background.disabled": "161a33a1c049fbf5a8a1d389f9b521d32c6b97d6", "buttons.split.primary.color.background.attention": "c9df8882251059668fa3d38806fa0ccbf217d653", "buttons.split.primary.color.background.attention-hover": "f863b5081d58e072478d9bd169f28da68a3250a7", "buttons.split.primary.color.label.default": "dbfd09cca93c3a2a2ac67da108b1ee1184686fec", "buttons.split.primary.color.label.hover": "da8da475edff88773f3c6bc4d79a56006cc1296a", "buttons.split.primary.color.label.disabled": "e3f62e35dc73111f3ce2d66d3be28d85aaa02362", "buttons.split.primary.color.label.attention": "2afca8ec39247abd9048626da602d6df31bd877d", "buttons.split.primary.color.label.attention-hover": "e85b42f3c6691d0e86869d4c57831e090c606363", "buttons.split.primary.color.icon.default": "aa7dd8c927b9c6f240e81a5dbd3abcf3403d853b", "buttons.split.primary.color.icon.hover": "31aefa4f59805a586a5b2b31c30a680c49f7514f", "buttons.split.primary.color.icon.disabled": "4a4d6cf830592f79a1fc77c01729b359e90cf370", "buttons.split.primary.color.icon.attention": "d44602622917f35d89c5658c6e5c613f57f9051a", "buttons.split.primary.color.icon.attention-hover": "06b494a7689e7b10352909ad6d89dde48350a81c", "buttons.split.primary.color.separator.default": "7f5a5dced7557e1b2ed9e2cad9757e066566c5d7", "buttons.split.primary.color.separator.hover": "98d0d4aeefe164bc648dfad2c6897953bd08875e", "buttons.split.primary.color.separator.disabled": "1b33d34c08a64a0887e9c6e83ef6871d1b1cd362", "buttons.split.primary.color.separator.attention": "b22819be0c486ee4a019ca9f11139e2587c9e3e7", "buttons.split.primary.color.separator.attention-hover": "d2c4ce811a5ca5cb304700e605f0acedb44245ff", "buttons.split.secondary.color.background.hover": "e7d26ebc24d3902b3865992a9ca40318aacdfee4", "buttons.split.secondary.color.background.attention-hover": "9aed6293b6464f378aed777091ff568e0835033e", "buttons.split.secondary.color.border.default": "bb936b92b35b34b76e51cdee5ce790a5145bce0f", "buttons.split.secondary.color.border.hover": "df9237658edfdf597765bdf1f9bd5431303418db", "buttons.split.secondary.color.border.disabled": "8961c7ee2603b84d121f580809d0964685107fea", "buttons.split.secondary.color.border.attention": "b09fed11ac333427b0e15475ec961f6d5a7d6f8d", "buttons.split.secondary.color.border.attention-hover": "d1e6e32e40017030410e20d333d6585d00dc062c", "buttons.split.secondary.color.label.default": "a3c0927b19ba19fe1263a4ba336d36ecfbae253d", "buttons.split.secondary.color.label.hover": "1e21bc49d438ce0f61c9ce275023544b2ac73cdf", "buttons.split.secondary.color.label.disabled": "b5fe6e57b40da44a32b2dbe765ec95cc6a8b826b", "buttons.split.secondary.color.label.attention": "208ff6eb6ae2d3f6eb0aaf70f32abfb8944f84b7", "buttons.split.secondary.color.label.attention-hover": "f0501253a157187f18bd7d26abf63f0c8e2d547f", "buttons.split.secondary.color.icon.default": "09b86d6d44465b2efd237c7bbd1fad2e1e0397f6", "buttons.split.secondary.color.icon.hover": "28d66664e6ef770e78ddd5491c75cd159b879069", "buttons.split.secondary.color.icon.disabled": "262b19cbd0252fcd9502a81d4d36b23e277555aa", "buttons.split.secondary.color.icon.attention": "29a2834d73421029d966a92cad58a3c19c9f461c", "buttons.split.secondary.color.icon.attention-hover": "004a053d8bce618a900fe071a0c720295c700fca", "buttons.split.secondary.color.separator.default": "958ea51f7a6636373eb27c83151503b320a1c581", "buttons.split.secondary.color.separator.hover": "39ca547576d51b8150c6287c7db051f6dada1a3d", "buttons.split.secondary.color.separator.disabled": "d88df8dc64c7cf0b63564ce3988e36feade95337", "buttons.split.secondary.color.separator.attention": "f8c713515f5cf3cd2e402ec6d7f502d0d02ed86f", "buttons.split.secondary.color.separator.attention-hover": "b40d7e83df4626d4a70dad8012dd0f5bcca0d0c4", "buttons.split.informative.color.background.default": "02623a101b7103c56b46f5dde6cf6e5cd5245e77", "buttons.split.informative.color.background.hover": "d6c55eef761c48a9e1c16e75ad084613033c7f4d", "buttons.split.informative.color.background.disabled": "1b8ea92d953f0f7d2a8f998ed57e781c06629bf1", "buttons.split.informative.color.background.attention": "addf5439487f7f6108587e3d6a7935850cb91f23", "buttons.split.informative.color.background.attention-hover": "650ead828894518c90237beabe6abb63dbdd46f2", "buttons.split.informative.color.label.default": "255006e8389a9f711b34816af7630e5c736430eb", "buttons.split.informative.color.label.hover": "23c1f552199c7cbf6f1b10ec95428e64e0120e98", "buttons.split.informative.color.label.disabled": "bbd0f7a5f373399cff8ea839c0d939e84a1e9f2d", "buttons.split.informative.color.label.attention": "25fc55304f9e4af5ef1d07b8aec670b812f20e38", "buttons.split.informative.color.label.attention-hover": "61afaae9c71d77504d70e053eeeca867e27c078d", "buttons.split.informative.color.icon.default": "0a865ffe54fb622da81288c5ac5b73d448c8407b", "buttons.split.informative.color.icon.hover": "b5bc44993d9a39b6a8eb8e8bf67b2101c6570e5b", "buttons.split.informative.color.icon.disabled": "a41905d8b7d5a503d1cf6b4784c691d950eb755b", "buttons.split.informative.color.icon.attention": "114269f9d374934fd822dc8368d4f53aef14b1ea", "buttons.split.informative.color.icon.attention-hover": "e30d49bc528c55d3f259ea01995f2367f17729c7", "buttons.split.informative.color.separator.default": "83119baba4c653ac105000d1cb660e069716685b", "buttons.split.informative.color.separator.hover": "daebb9fdd7b362a594cbc4735373a05ac476f6c5", "buttons.split.informative.color.separator.disabled": "7e945709d9ba0f1094271dceace05914bbec4c9f", "buttons.split.informative.color.separator.attention": "6df3f4d46f506538ca9fc80dc8329d19fbf69120", "buttons.split.informative.color.separator.attention-hover": "48ccd7a48270e8205021c9542657d7a66dd9d8ed", "buttons.action.primary.color.background.hover": "a05b1ad5759521cec75a2cc4f6b9f49f2d2098cc", "buttons.action.primary.color.icon.default": "8639a0382492df9667dce6c3c17d07541e109ffa", "buttons.action.primary.color.icon.hover": "e867c4a5b83ee9fb0d119d04e85b6ba1e99060f3", "buttons.action.primary.color.icon.disabled": "4e0d0bd29dcb2a44e2c9fccee908dd350ff9de0f", "buttons.action.primary.color.icon.attention": "036235a15d5903b2146cd21a8928ad1247d1f6a9", "buttons.action.primary.color.icon.attention-hover": "3045e7b2d45286f47ea38fc47be0bcb745b2f8a7", "buttons.action.informative.color.background.hover": "e0f7179e77685726db0b736042bd5a436d3560a8", "buttons.action.informative.color.icon.default": "7f44fbed8939243f448e1538963f0e30cc566278", "buttons.action.informative.color.icon.hover": "c5706881232effeca0c9c4a41d42e2082314df9f", "buttons.action.informative.color.icon.disabled": "367d232565ced6951bba69fd74cad82f0b04b296", "buttons.action.informative.color.icon.attention": "e0d2ca923d5b1badb02067d0ce9e1fb646d982ad", "buttons.action.informative.color.icon.attention-hover": "27cf7410bb7553d8eaf480ebf57f48edad187af4", "buttons.action.primary.color.background.attention-hover": "3c528f677ca6c3c4422484484663c7a64b27ed82", "buttons.action.informative.color.background.attention-hover": "59213e48f855aa396f48fce6fd437f96b5dc8c91", "buttons.action.tertiary.color.background.hover": "7e48a5ac5129b8f4fa3b865e8f1072042a6515f2", "buttons.action.tertiary.color.background.attention-hover": "9abff638d670a18b9c5d3270cb333e7bfdc220f8", "buttons.action.tertiary.color.icon.default": "779b7f116c84367b38034582b855dd9a7a20c457", "buttons.action.tertiary.color.icon.hover": "7e4ba87ad04f87b17794e3818c7b3849a2e97740", "buttons.action.tertiary.color.icon.disabled": "61722d78768de5760f61d0a1ee3ce626354465ef", "buttons.action.tertiary.color.icon.attention": "2698d10ab3b9d9f7e0338662b8fe4c06fb4e3e53", "buttons.action.tertiary.color.icon.attention-hover": "2b49d00b4ba463fbc045b38b428911e7f60bce2d", "illustrations.business-type.color.secondary": "2d29e782562d3ca67faadce3a99d5f49bb72d116", "illustrations.business-type.color.light": "8a293d19ca5c4b5dd06a44e7e865ed0d889efd2d"}, "$figmaModeId": "31683:0", "$figmaCollectionId": "VariableCollectionId:5120:13789"}, {"id": "3-component-sparkasse", "name": "sparkasse", "group": "3-Component", "selectedTokenSets": {"3-Component/sparkasse": "enabled"}, "$figmaStyleReferences": {}, "$figmaVariableReferences": {"buttons.main-set.primary.color.background.default": "0d3c773813279ef2bef9038ee106cbc5f00f7bd7", "illustrations.business-type.color.primary": "3eb9384d4837a7d62f36d2ff7a7c564bfb6b5253", "buttons.main-set.primary.color.background.hover": "e51e22702c756b4682fe51406b504056946adbcd", "buttons.main-set.primary.color.background.disabled": "fd0d9d040c16a2057f68eb5458f7f9ae302aa868", "buttons.main-set.primary.color.background.attention": "f957a172a5d754856408af22850682ef67efe4a2", "buttons.main-set.primary.color.background.attention-hover": "2196e9b3227f2de008d064e4da45502eec41aa9a", "buttons.main-set.primary.color.label.default": "a6b4196d4779f473926ab354d097b8050bdabf45", "buttons.main-set.primary.color.label.hover": "ec6df9e67c690d6e510d594f5df439742c8116f7", "buttons.main-set.primary.color.label.disabled": "8e89d7c7c1487775764aa0a879913d43de3a8f2c", "buttons.main-set.primary.color.label.attention": "6a49f4897bc423370839e5b47512b94ce6a4f3ef", "buttons.main-set.primary.color.label.attention-hover": "363613bf980dcfbf99e240d4c7ca343bb01b3a42", "buttons.main-set.secondary.color.background.attention-hover": "72281067bacc1d75051c1de3b366dfafa4022bd5", "buttons.main-set.secondary.color.background.hover": "0e2826c992cbc72442150a4c95df160f98ae8943", "buttons.main-set.secondary.color.border.attention-hover": "89645cffdd3253d31c4ad189e97ed60890eb531a", "buttons.main-set.secondary.color.border.attention": "1b60d08725027a18ef60133e7fb4312420d1d2b1", "buttons.main-set.secondary.color.border.disabled": "4dcb660451c577e4cf4b999c644bee3b15b166f4", "buttons.main-set.secondary.color.border.hover": "c697eb9e8a7c9d47c2453afcb627a21fd635cfc4", "buttons.main-set.secondary.color.border.default": "54313c38009de21e4e158d47e7c6e15583b86074", "buttons.main-set.secondary.color.label.attention-hover": "3790bfb900d38b5667d082ccaaa0551adeecff44", "buttons.main-set.secondary.color.label.attention": "a5fc04e657b4e07edd1ea04a2678d84c196326b7", "buttons.main-set.secondary.color.label.disabled": "5b4b08f8d4f2d9bd5b5ca63269fff1f9b0ac763f", "buttons.main-set.secondary.color.label.hover": "91771be8edf13973331d7383269b9f08ba5d7e1d", "buttons.main-set.secondary.color.label.default": "9d3db32c01ed611dafc70d6d976c9918603be9d9", "buttons.main-set.informative.color.background.default": "602a1000d1cf485952a7863a0c607cc42bb4e503", "buttons.main-set.informative.color.background.hover": "d53a92336ba6f0af47f367b53d58f5393eb95d5e", "buttons.main-set.informative.color.background.disabled": "ce7e98fe0e79e0da264608715e2f24c70f2ac7ea", "buttons.main-set.informative.color.background.attention": "b318882e3b3fecb4045149418aff77c68e3e53d4", "buttons.main-set.informative.color.background.attention-hover": "a5ba5142081ad1268edbd95264492133674b3c99", "buttons.main-set.informative.color.label.default": "71bb422e6cc526a1d5ac606983d2ae3c19410146", "buttons.main-set.informative.color.label.hover": "bbc78c45330bdc3481d0e5f5e54a8791164d72c1", "buttons.main-set.informative.color.label.disabled": "881f271525460f9ccd8dd17ec546c1bed8f8629d", "buttons.main-set.informative.color.label.attention": "9e0921d89a6029a2316f2810a2c6943b38b84403", "buttons.main-set.informative.color.label.attention-hover": "3489deb36c104a039b7b532f20211adad5a9e7ba", "buttons.main-set.stealth.color.background.hover": "03293fca2e3f668d644bfdbc2a1831db7e3fa9ab", "buttons.main-set.stealth.color.background.attention-hover": "59c27a0922b06d5f366e9c6418bb51b33a222f7c", "buttons.main-set.stealth.color.label.default": "fa0bad2a89768ea3176ec9a85a5717d682c9ac36", "buttons.main-set.stealth.color.label.hover": "ea51541daa7e5f81d7858c8e57df514780fadbc2", "buttons.main-set.stealth.color.label.disabled": "3357cf13b780b9247f2912259eeb10c84042f90a", "buttons.main-set.stealth.color.label.attention": "093d7d71e44fc24dede6bfc06f3c0e0960777ba7", "buttons.main-set.stealth.color.label.attention-hover": "ac6e436c8c8050c3c0e042ebe53de4ee80f6f37f", "buttons.icon-only.primary.color.background.default": "66cf8a49f94f6ee382e58a4d064104736e4f59f1", "buttons.icon-only.primary.color.background.hover": "94a3031105d5fa27bedcc86d1604464307a49cd4", "buttons.icon-only.primary.color.background.disabled": "35cba3d9ec05d34efdf19282701a8baec5d50450", "buttons.icon-only.primary.color.background.attention": "92a2d39a05cef072d818a2ce031a75f4a9b83cd3", "buttons.icon-only.primary.color.background.attention-hover": "e3ba0ec5e48d2b49af3c34b06217716270149582", "buttons.icon-only.primary.color.icon.default": "6bfe0e4066947d5ca0a4b13733c729855ffaf1d8", "buttons.icon-only.primary.color.icon.hover": "92873b38f78ee438ec284661a8af6c402951f762", "buttons.icon-only.primary.color.icon.disabled": "1ffaeacb2b9b3161e561d9b2c3fec86f805e3f50", "buttons.icon-only.primary.color.icon.attention": "4ead26e4dbb24ec64bb0d29a7b33afcba3bc754f", "buttons.icon-only.primary.color.icon.attention-hover": "041a46e2d53ad2843b006686fc66dad7e3772ee5", "buttons.icon-only.informative.color.background.default": "8c150fb4f3309a4a5d975f155582435c18e17a1e", "buttons.icon-only.informative.color.background.hover": "715836be0e039546ded312125546606936580b57", "buttons.icon-only.informative.color.background.disabled": "44ed2fab389e7afafb84eec4c58543677b7b8939", "buttons.icon-only.informative.color.background.attention": "6c336cc7c946725b8b2e98590c35118f52754f30", "buttons.icon-only.informative.color.background.attention-hover": "600c1857a565032df81b11b8439e16976c48a131", "buttons.icon-only.informative.color.icon.default": "ce17069850fd749bd89a0e9e250ae0473592809d", "buttons.icon-only.informative.color.icon.hover": "cd2efabce64ba433f77ed7e83337bda6244ec7d7", "buttons.icon-only.informative.color.icon.disabled": "473cac7ee956ac6d27de1b7344dab2cdc5445812", "buttons.icon-only.informative.color.icon.attention": "6aff4d65194300adc18d6f457c5f9c839da6d938", "buttons.icon-only.informative.color.icon.attention-hover": "fbd16b54a6986aff803999442c74e7ea8def14aa", "buttons.main-set.primary.color.icon.default": "61d997b4c593629d3d548d3f5446165e47f36f4c", "buttons.main-set.primary.color.icon.hover": "7fe48e90665eb7e966bc40c9fe4ec87efdccd62c", "buttons.main-set.primary.color.icon.disabled": "5fdada702fe7b0f99560980f918a40609f6b06a4", "buttons.main-set.primary.color.icon.attention": "3ac508c76ed8c6db1da33206f909989742550082", "buttons.main-set.primary.color.icon.attention-hover": "5e697a195c2c05670651efa7852c5672c387a0ab", "buttons.main-set.secondary.color.icon.default": "b1bfff5e10b29285166ebc40751fcbdf0286d0b6", "buttons.main-set.secondary.color.icon.hover": "4a1a1851501db4a2f9591706513b784423eeb2a1", "buttons.main-set.secondary.color.icon.disabled": "2b60c2d00ec9a2c1830c99c99e1811d6ed33545d", "buttons.main-set.secondary.color.icon.attention": "8bf79dd98f57f8b09dfeb3027efe67067c15c432", "buttons.main-set.secondary.color.icon.attention-hover": "9289a2ffed6e28daf4a5456ca051e3489f681e60", "buttons.main-set.informative.color.icon.default": "d62880d9dd2be75da6d34c1493465d31e558aec1", "buttons.main-set.informative.color.icon.hover": "879fe098934952543a3571aa9ef32393f6c46c55", "buttons.main-set.informative.color.icon.disabled": "08ea8b0eceb885ebd78c73f5829f8c8e910ea766", "buttons.main-set.informative.color.icon.attention": "1a145f52d62cd39055cdcccfb63cacfbac0ec513", "buttons.main-set.informative.color.icon.attention-hover": "9c4aafe7c75968ae23df49331ac599f5e64dabe1", "buttons.main-set.stealth.color.icon.default": "31a8c054d4bb7bacaa9f7e364b708b3f49693233", "buttons.main-set.stealth.color.icon.hover": "b87dc242a3f711b460f18a5c6cd5ff18d7c1b42f", "buttons.main-set.stealth.color.icon.disabled": "7d17b6ce461c0061f6062abdecb234edf45adff9", "buttons.main-set.stealth.color.icon.attention": "407a7c31c41b7307705555e04be47d15235b69b5", "buttons.main-set.stealth.color.icon.attention-hover": "ed7b278d3230c563597ac92ebf3dc422a0b9afdb", "buttons.icon-only.secondary.color.background.hover": "739bf8b0c1910653f403a520e79b91d1d8439a3f", "buttons.icon-only.secondary.color.background.attention-hover": "457d24fc8b47961844e0892c6d42fe84d4a71cd7", "buttons.icon-only.secondary.color.border.default": "f3ce1699b2cb2a28d735fd28cb06e6e3e41d2aee", "buttons.icon-only.secondary.color.border.hover": "f714c31a1c0124187f6acdec4d207932f467d3ad", "buttons.icon-only.secondary.color.border.disabled": "81e3fb7c90d8c1ff08c3df543de0fa64403bbe2c", "buttons.icon-only.secondary.color.border.attention": "83084fc351532e62ffe8cb4efb20224db027aadb", "buttons.icon-only.secondary.color.border.attention-hover": "a93871c8e46358af2dc356da20900964f773e48a", "buttons.icon-only.secondary.color.icon.default": "3ade2e6f9a641b8ade46e00bf9ea6700ec264203", "buttons.icon-only.secondary.color.icon.hover": "35d43617761a4e91b42474f68b1a98cc6c5bcb91", "buttons.icon-only.secondary.color.icon.disabled": "f69efb95ae277023298a967f4fabe85a3e2cfd48", "buttons.icon-only.secondary.color.icon.attention": "c5efc9e3f1c84f16c58083e00f8ddc689ffabc93", "buttons.icon-only.secondary.color.icon.attention-hover": "13050af2d58bc00608508949ab3dce93c0ed5405", "buttons.icon-only.stealth.color.background.hover": "4f3958b29bd657136b7ea55ff9ff1b024f472a6c", "buttons.icon-only.stealth.color.background.attention-hover": "1b0da33a0794793c5f40f1a7f0db159dc8de4a9c", "buttons.icon-only.stealth.color.icon.default": "fbdd7be4c8d2a2472fdaabfa02f153f22249174e", "buttons.icon-only.stealth.color.icon.hover": "cf113600f58b563d4b83e3e5f183863d75b0dbbb", "buttons.icon-only.stealth.color.icon.disabled": "7e0c30b2ab59d27f2c94744e8f081fe5843569ff", "buttons.icon-only.stealth.color.icon.attention": "021c4f68fb82e816aed018f5a028bd9a291abe0f", "buttons.icon-only.stealth.color.icon.attention-hover": "5095c8ed7c6401a8b24d737e60e4a7b49f8da51d", "buttons.fab.color.background.default": "0901c5ae2818f392cb70079010b92bc211672c1f", "buttons.fab.color.background.hover": "0ae88f2c4a4b70b9e51b54126d3756d079f9f09a", "buttons.fab.color.icon.default": "66199232e7695b2023ae8964687b7d35a96e0a81", "buttons.fab.color.icon.hover": "258149e0043066871a179d9c14379554838e33fd", "buttons.split.primary.color.background.default": "b7e486d0b0a92711f645e4481b0e55cd9e98e908", "buttons.split.primary.color.background.hover": "9c1719f811fc5ae427b459575bb0bd8e8f20d4f3", "buttons.split.primary.color.background.disabled": "161a33a1c049fbf5a8a1d389f9b521d32c6b97d6", "buttons.split.primary.color.background.attention": "c9df8882251059668fa3d38806fa0ccbf217d653", "buttons.split.primary.color.background.attention-hover": "f863b5081d58e072478d9bd169f28da68a3250a7", "buttons.split.primary.color.label.default": "dbfd09cca93c3a2a2ac67da108b1ee1184686fec", "buttons.split.primary.color.label.hover": "da8da475edff88773f3c6bc4d79a56006cc1296a", "buttons.split.primary.color.label.disabled": "e3f62e35dc73111f3ce2d66d3be28d85aaa02362", "buttons.split.primary.color.label.attention": "2afca8ec39247abd9048626da602d6df31bd877d", "buttons.split.primary.color.label.attention-hover": "e85b42f3c6691d0e86869d4c57831e090c606363", "buttons.split.primary.color.icon.default": "aa7dd8c927b9c6f240e81a5dbd3abcf3403d853b", "buttons.split.primary.color.icon.hover": "31aefa4f59805a586a5b2b31c30a680c49f7514f", "buttons.split.primary.color.icon.disabled": "4a4d6cf830592f79a1fc77c01729b359e90cf370", "buttons.split.primary.color.icon.attention": "d44602622917f35d89c5658c6e5c613f57f9051a", "buttons.split.primary.color.icon.attention-hover": "06b494a7689e7b10352909ad6d89dde48350a81c", "buttons.split.primary.color.separator.default": "7f5a5dced7557e1b2ed9e2cad9757e066566c5d7", "buttons.split.primary.color.separator.hover": "98d0d4aeefe164bc648dfad2c6897953bd08875e", "buttons.split.primary.color.separator.disabled": "1b33d34c08a64a0887e9c6e83ef6871d1b1cd362", "buttons.split.primary.color.separator.attention": "b22819be0c486ee4a019ca9f11139e2587c9e3e7", "buttons.split.primary.color.separator.attention-hover": "d2c4ce811a5ca5cb304700e605f0acedb44245ff", "buttons.split.secondary.color.background.hover": "e7d26ebc24d3902b3865992a9ca40318aacdfee4", "buttons.split.secondary.color.background.attention-hover": "9aed6293b6464f378aed777091ff568e0835033e", "buttons.split.secondary.color.border.default": "bb936b92b35b34b76e51cdee5ce790a5145bce0f", "buttons.split.secondary.color.border.hover": "df9237658edfdf597765bdf1f9bd5431303418db", "buttons.split.secondary.color.border.disabled": "8961c7ee2603b84d121f580809d0964685107fea", "buttons.split.secondary.color.border.attention": "b09fed11ac333427b0e15475ec961f6d5a7d6f8d", "buttons.split.secondary.color.border.attention-hover": "d1e6e32e40017030410e20d333d6585d00dc062c", "buttons.split.secondary.color.label.default": "a3c0927b19ba19fe1263a4ba336d36ecfbae253d", "buttons.split.secondary.color.label.hover": "1e21bc49d438ce0f61c9ce275023544b2ac73cdf", "buttons.split.secondary.color.label.disabled": "b5fe6e57b40da44a32b2dbe765ec95cc6a8b826b", "buttons.split.secondary.color.label.attention": "208ff6eb6ae2d3f6eb0aaf70f32abfb8944f84b7", "buttons.split.secondary.color.label.attention-hover": "f0501253a157187f18bd7d26abf63f0c8e2d547f", "buttons.split.secondary.color.icon.default": "09b86d6d44465b2efd237c7bbd1fad2e1e0397f6", "buttons.split.secondary.color.icon.hover": "28d66664e6ef770e78ddd5491c75cd159b879069", "buttons.split.secondary.color.icon.disabled": "262b19cbd0252fcd9502a81d4d36b23e277555aa", "buttons.split.secondary.color.icon.attention": "29a2834d73421029d966a92cad58a3c19c9f461c", "buttons.split.secondary.color.icon.attention-hover": "004a053d8bce618a900fe071a0c720295c700fca", "buttons.split.secondary.color.separator.default": "958ea51f7a6636373eb27c83151503b320a1c581", "buttons.split.secondary.color.separator.hover": "39ca547576d51b8150c6287c7db051f6dada1a3d", "buttons.split.secondary.color.separator.disabled": "d88df8dc64c7cf0b63564ce3988e36feade95337", "buttons.split.secondary.color.separator.attention": "f8c713515f5cf3cd2e402ec6d7f502d0d02ed86f", "buttons.split.secondary.color.separator.attention-hover": "b40d7e83df4626d4a70dad8012dd0f5bcca0d0c4", "buttons.split.informative.color.background.default": "02623a101b7103c56b46f5dde6cf6e5cd5245e77", "buttons.split.informative.color.background.hover": "d6c55eef761c48a9e1c16e75ad084613033c7f4d", "buttons.split.informative.color.background.disabled": "1b8ea92d953f0f7d2a8f998ed57e781c06629bf1", "buttons.split.informative.color.background.attention": "addf5439487f7f6108587e3d6a7935850cb91f23", "buttons.split.informative.color.background.attention-hover": "650ead828894518c90237beabe6abb63dbdd46f2", "buttons.split.informative.color.label.default": "255006e8389a9f711b34816af7630e5c736430eb", "buttons.split.informative.color.label.hover": "23c1f552199c7cbf6f1b10ec95428e64e0120e98", "buttons.split.informative.color.label.disabled": "bbd0f7a5f373399cff8ea839c0d939e84a1e9f2d", "buttons.split.informative.color.label.attention": "25fc55304f9e4af5ef1d07b8aec670b812f20e38", "buttons.split.informative.color.label.attention-hover": "61afaae9c71d77504d70e053eeeca867e27c078d", "buttons.split.informative.color.icon.default": "0a865ffe54fb622da81288c5ac5b73d448c8407b", "buttons.split.informative.color.icon.hover": "b5bc44993d9a39b6a8eb8e8bf67b2101c6570e5b", "buttons.split.informative.color.icon.disabled": "a41905d8b7d5a503d1cf6b4784c691d950eb755b", "buttons.split.informative.color.icon.attention": "114269f9d374934fd822dc8368d4f53aef14b1ea", "buttons.split.informative.color.icon.attention-hover": "e30d49bc528c55d3f259ea01995f2367f17729c7", "buttons.split.informative.color.separator.default": "83119baba4c653ac105000d1cb660e069716685b", "buttons.split.informative.color.separator.hover": "daebb9fdd7b362a594cbc4735373a05ac476f6c5", "buttons.split.informative.color.separator.disabled": "7e945709d9ba0f1094271dceace05914bbec4c9f", "buttons.split.informative.color.separator.attention": "6df3f4d46f506538ca9fc80dc8329d19fbf69120", "buttons.split.informative.color.separator.attention-hover": "48ccd7a48270e8205021c9542657d7a66dd9d8ed", "buttons.action.primary.color.background.hover": "a05b1ad5759521cec75a2cc4f6b9f49f2d2098cc", "buttons.action.primary.color.icon.default": "8639a0382492df9667dce6c3c17d07541e109ffa", "buttons.action.primary.color.icon.hover": "e867c4a5b83ee9fb0d119d04e85b6ba1e99060f3", "buttons.action.primary.color.icon.disabled": "4e0d0bd29dcb2a44e2c9fccee908dd350ff9de0f", "buttons.action.primary.color.icon.attention": "036235a15d5903b2146cd21a8928ad1247d1f6a9", "buttons.action.primary.color.icon.attention-hover": "3045e7b2d45286f47ea38fc47be0bcb745b2f8a7", "buttons.action.informative.color.background.hover": "e0f7179e77685726db0b736042bd5a436d3560a8", "buttons.action.informative.color.icon.default": "7f44fbed8939243f448e1538963f0e30cc566278", "buttons.action.informative.color.icon.hover": "c5706881232effeca0c9c4a41d42e2082314df9f", "buttons.action.informative.color.icon.disabled": "367d232565ced6951bba69fd74cad82f0b04b296", "buttons.action.informative.color.icon.attention": "e0d2ca923d5b1badb02067d0ce9e1fb646d982ad", "buttons.action.informative.color.icon.attention-hover": "27cf7410bb7553d8eaf480ebf57f48edad187af4", "buttons.action.primary.color.background.attention-hover": "3c528f677ca6c3c4422484484663c7a64b27ed82", "buttons.action.informative.color.background.attention-hover": "59213e48f855aa396f48fce6fd437f96b5dc8c91", "buttons.action.tertiary.color.background.hover": "7e48a5ac5129b8f4fa3b865e8f1072042a6515f2", "buttons.action.tertiary.color.background.attention-hover": "9abff638d670a18b9c5d3270cb333e7bfdc220f8", "buttons.action.tertiary.color.icon.default": "779b7f116c84367b38034582b855dd9a7a20c457", "buttons.action.tertiary.color.icon.hover": "7e4ba87ad04f87b17794e3818c7b3849a2e97740", "buttons.action.tertiary.color.icon.disabled": "61722d78768de5760f61d0a1ee3ce626354465ef", "buttons.action.tertiary.color.icon.attention": "2698d10ab3b9d9f7e0338662b8fe4c06fb4e3e53", "buttons.action.tertiary.color.icon.attention-hover": "2b49d00b4ba463fbc045b38b428911e7f60bce2d", "illustrations.business-type.color.secondary": "2d29e782562d3ca67faadce3a99d5f49bb72d116", "illustrations.business-type.color.light": "8a293d19ca5c4b5dd06a44e7e865ed0d889efd2d"}, "$figmaModeId": "38556:0", "$figmaCollectionId": "VariableCollectionId:5120:13789"}, {"id": "2-alias-<PERSON><PERSON><PERSON>", "name": "neoshare", "group": "2-<PERSON><PERSON>", "selectedTokenSets": {"2-Alias/neoshare": "enabled"}, "$figmaStyleReferences": {}, "$figmaVariableReferences": {"color.brand.primary": "7a595493d51bbce68c2256ae0824e9901dab77fc", "color.brand.dark": "d7cebcf0f2cf00b5360f193e1d8a6e0820928502", "color.brand.tertiary": "5f7939a94ef3f8c7baa88be056489189155c40c5", "color.brand.secondary": "6f70e17e8e9d5ca74affa719429be2a25ec7d3f8", "color.surface.primary": "43006e9bc2e917cec736d66a505c3e43e03643ae", "color.surface.secondary": "d620287b5aa9e3f1fccda24f8196563a26fbd75b", "color.surface.hover": "578bcab358b56a13b08643aaf2dfa697615c6d3b", "color.status.success": "9413efdcae8c9609190ec02e96e0a1a80f604928", "color.status.error": "52c63409b2d57fd4173bb8ce66920b54232f7fe5", "color.status.failure": "4d83e43dc9c411168354d0d5b03621038e812dc5", "color.status.warning": "2b6b8362e5de9c92cc086c32b5eb4c0ccbc2268e", "color.advanced-editor.first": "8122ff21744f4bcd501efd19f34b7b37351a101e", "color.advanced-editor.second": "66922e1c92633194341ee145d88ba6aba5f3164b", "color.advanced-editor.third": "9a910566b6d7ddb6e88af9df6a83aa1576f8f278", "color.advanced-editor.fourth": "39f9c9299bb0adcf1e06686c1cdc5238718eabd6", "color.advanced-editor.fifth": "138c4bb13f97596394235c46f66ddcf87056bff6", "color.advanced-editor.sixth": "d869309ca5016e83ea4245382b8aa101cf65b01e", "color.advanced-editor.seventh": "491e67da835cfcef4890fed054b71331b55f0112", "color.advanced-editor.eighth": "8ce46e05757cb4eae271dbbc6297622a8c51730e", "color.status.neutral": "fd76107eab94a41820dbdc6ca1b9061dbf0d7e1a", "color.status.informative": "f0b2fe02f2eb587a97c4d3468eb0d617f7172c1c", "color.surface.dark-strong": "600d29e8ada6303ff94b135d6c95e0694088123d", "color.text.tertiary": "81949ba703642f17e8dc89d873a42f56b4868938", "color.text.primary": "d08e8f3c7c79de97dedd82a96b7689a9c5fc234a", "color.text.interactive": "d94a119c1224e0795facad53f47b33e4801c9cf1", "color.text.light": "b15a65aa4b09782b80db2867621cad91202537ac", "color.text.secondary": "51e5060863602e4d48e0af2bc0199509984b0f4d", "color.surface.tertiary": "27acfe52e842d58ae25e9726ba2aaf1cc6115f5f", "color.surface.dark-subtle": "fa74bcab2fc59cd4b0c202e847bb2e46741ede5a", "color.border.default.primary": "d47f3250ec89f0f08ebad0cdc9ca536b08017164", "color.icons.primary": "11d3797c54235cc4de5e4eca91a582bbc4d161a3", "color.icons.disabled": "3858b9ca49dbb6dc8c0ad0676afd2967471d1dad", "color.border.default.inactive": "5e0d2aac2ffd3fcefc86e7a9f20a4335f6cdec84", "color.border.default.success": "6241b3b62c526509d24723fbbbfb0f5742520955", "color.border.default.error": "10c5752b42c5a95822d10eb74b71e03ce3c4cccf", "color.border.default.hover": "bb19a151787f28faca2dfed901bbf3eb8dd5b274", "color.text.disabled": "faf10cdadc8e0ece4bca186afe314f1a7b5ae76f", "color.text.success": "4102ed6aee5f71e92d253f35c98e445baffa58f7", "color.text.error": "ac945c2f698d057d2ec6f3bad31a5b552dcd7607", "color.icons.success": "807faf2916ea20a88fc1a001bc1ca1f0bc1d5af4", "color.icons.error": "1e5f96144cc642afeed2a4aa344c17ee29fdd3fa", "color.icons.brand-primary": "1ce0fd21ff86fe5e00f781f15a626253ebbb0862", "color.icons.interactive": "0401d0c8674a0c5ca38843fdcabd130a2f1fc4a2", "color.icons.light": "049180a6d182cd45bca8b4a69eeb00e9666036e1", "color.border.default.warning": "00a6d38105c17453e719856c773bc2f3febf40c9", "color.background.primary-strong": "e59c6659fbaf0ee9f2c120a4f2f940b688142a5c", "color.background.secondary-strong": "71fa46a6e2fb4eb7ba169e29e3058e06726f2dfb", "color.background.tertiary-subtle": "d7efc69a77181b07b35ff1eca0ed1ced0acd0986", "color.background.disabled": "ce6c0697645b418d8d656888606ac8c2b8565aee", "color.hover.primary": "c349f0e12c90338f9040d50b668a9b32aaf3276c", "color.hover.tertiary": "eef5af69935fd957411d636f968a2c6f864210f2", "color.hover.attention": "5ac1504be02e195c11da3238fdb2eec80a19a75d", "color.background.dark-strong": "1392680d15dd3c302c2a71ec2039ae289042118e", "color.background.primary-moderate": "d2cfb283ab716d257e237d86c89b48d2fe983aeb", "color.background.secondary-subtle": "ae7c029bf0e3c11b60466059a39eb482849e6823", "color.background.dark-subtle": "a292a2c3ea0eab7c108fa291790699d0b924b1ca", "color.background.warning-strong": "afa449474d3706f2e3c134bdc19a1e9bfd526e23", "color.background.warning-subtle": "2e6df71237f08ea389a3c5fbdaebe5d6a6357b65", "color.background.success-strong": "79e9714a0d52bdc7c0af5ca924a75de81a36fdc1", "color.background.success-subtle": "26ecc99c8a87c3644159dafa396c85173ebda629", "color.background.failure-strong": "352862c27ef4f71ba77f57e29ec071f6d6e69c4c", "color.background.failure-subtle": "744242d2fd69ff4223679fade42c17b6bc655527", "color.background.attention-strong": "ac7b8357be682117f54386a41d0f5fab54afa493", "color.background.attention-subtle": "cfdc830a33d71dd7397bcc3ddcab2f1b6e2b2c3f", "color.text.warning": "8c38e64643394d06cf1456d172f934940ee401d4", "color.icons.tertiary": "f33dd353bb9dc1e48e6480c2d3c3cbf5c27f8a03", "color.background.neutral-minimal": "ce8c021ea5481de71d20740bb5ac2f7329de2a6f", "color.hover.secondary": "f47df59a98210900cb45f9b69f011bd8c889a17d", "color.background.light": "de29e924479bf8b619ab409426e187fe090137d1", "color.border.default.minimal": "b51cb970b5663ed2cea8390e86e3bf94d44a3a09", "color.border.default.interactive": "61ceea6614d2ab0894100161c7275a673bd0ba63", "color.icons.dark": "4fec5ae68afb2ea3c1c312a68e61b38daebe816f", "color.background.tertiary-strong": "05d5dd81737d95d939591842d5a925eafbe00d4f", "color.icons.minimal": "7f6582aa15306f74fa118e88355edffa41d38a30", "color.icons.secondary": "8d25dab8d222bd8912292b1f7b4b4b4ad010b697", "color.background.dark-moderate": "620be1a3abafee4c0c953090341728c61c137476", "color.background.dark-bold": "d4dc16007d56f103153a0ef5bb1a38512753d958", "color.text.minimal": "52706eb6a9b50a242baf57122888cc904aaef875", "color.border.default.light": "387746e7dba9f28c8d38e5d799e7f4672cd7a95f", "color.border.default.strong": "dcbf6b53cc3af79b049ccc356fc6c174901129df", "color.background.primary-minimal": "02ab621f5c1eb082a7786dd5f52871553b56562e", "color.background.secondary-minimal": "94e83de895715c7527953bc7fac64bfeffd5798b", "color.background.tertiary-minimal": "18cae2ee89e3681acab176d53da14fce3cd08692", "color.background.attention-minimal": "46b7298de8e61cc7b65b953e86721ed917768544", "color.background.warning-minimal": "8594b8321a57f72d602b9658a7728254506cf559", "color.background.success-minimal": "d275335e2bb140a3471f36157969dee2b4bc3819", "color.background.failure-minimal": "f4f09882a48b69f8c58664139dddceb7922587f4", "color.icons.warning": "c2ddefe9b96b4bd699afb0812415f7b7e7ab8c21", "size.spacing.none": "a3a0b08a76e0668f329b4ac0e3da8b6f42072fbf", "size.spacing.2": "439440036f5b76d630cede677a3d1c9c873806d7", "size.spacing.4": "d6c617c75b738748a3347ddb14be4c89f52251e1", "size.spacing.6": "9f246fad69de74c423be941317d9c2559c874261", "size.spacing.8": "926ef6756f9321285329aa33fd23fb0ddfb47cdc", "size.spacing.12": "ac3764ceb2d369e082b66180501996262ed7aa17", "size.spacing.16": "4341f29208c64b4cf39e466c74179e84d3c52bd3", "size.spacing.20": "1f7b7e3e2e7de69f892a48927d9af44250da4745", "size.spacing.24": "22ce4195de314151a4db59e5c7ff28204e08379f", "size.spacing.32": "432c70f5de429485ce2b5841820cd3e781ee5fa1", "size.spacing.40": "edd2551767d33f78f0d7e3a32afb55a0d5230c15", "size.spacing.48": "46b39cfec86d7fffc980fea8f0e97162bb5f01e5", "size.spacing.56": "4bf76485136feb0e4bfdaf366d86f32694803fac", "size.spacing.64": "d5376e5a5327f3fa0021f36c93eb6dfe8d70602e", "size.spacing.80": "51164f126877c48023d939d19f365076cec02d29", "size.spacing.120": "1caacf92a91a5e266f964c86a67dd9b13de2ce9a", "size.border.none": "36037156cf600f2b00e86fc8720ab074c3488461", "size.border.s": "4c26fb4430a552867974fa7d6894f659a0ac1170", "size.border.m": "45cd8d9b257639e7c893313c442b3aaf97f35f1a", "size.corner-radius.square": "51a405a88181625daa989830bfe3c1ed9ea41a22", "size.corner-radius.s": "8e2ecc489ddda461f3edd9c60be4245a06a27eb0", "size.corner-radius.m": "cb5d25bdfbef0d98fcc676fd502f32b5dbd6c003", "size.corner-radius.l": "c4dc835b27c13440937ee4a1e3cf80be6e8199a1", "size.corner-radius.round": "362e5b4133cff7eb7de3f6c75d27e5245bf2cc7b", "color.transparency.attention-subtle": "347243bbb7d1718657a3651910d6d15e75245a4f", "color.transparency.attention-minimal": "2d6aa74744b399677fa4b6810602db798a4bac0c", "color.transparency.secondary-subtle": "b3dcbe1312bec8871b63ad3b1d80eaf1725d389b", "color.transparency.secondary-minimal": "bce1f8e125c6a3817ccf571660b6cdeab399fb96", "color.transparency.dark-subtle": "458c8dd24de4743a8f3c2e57a1e55af7d54be8d0", "color.transparency.dark-bold": "ff6b000c845ca8f3a16b7405cb1ad9cfcaa11b88", "color.background.primary-subtle": "2d74a82a1adef82bffd9b9fec5b9d1c96f92c7cc", "color.background.secondary-moderate": "20efc540ce95e8bd6328f28ab5bde864b45784f2", "color.background.tertiary-moderate": "631b3382fa7b7ed48a325252a3d6296bed069be5", "color.background.attention-moderate": "89df70a74e99a721a3fe43e744fd2687ec5c2e8f", "color.background.warning-moderate": "2998cdabd68745ceac2204ef0f70d998b07df08a", "color.background.success-bold": "e11b4dbe3ab8c37979af6ad89126927988b7a0cb", "color.background.failure-moderate": "8681602703555cffa9415b81bb3ae1650b18c094", "color.background.primary-bold": "c59139f0752b50b927de700369949343268b230c", "color.background.secondary-bold": "651cd8dc0c23568d69882c28ed388133821075a7", "color.background.tertiary-bold": "d236ab352f6093d0504cb2a89f2cf733a5a2e4a3", "color.background.attention-bold": "4d97e03a9707af6c6b587bc31b0cb89b042f227d", "color.background.warning-bold": "aa95a901cd10c7ef8c87df1f82193281fdd66c22", "color.background.success-moderate": "0d28059ecff94cce1bd62fb48337475c1a1b2aff", "color.background.failure-bold": "42bd287cea157ec716d32ed57b557dadcc6a2dd2", "size.spacing.18": "ea4451c399d250264f9112ae98f0d18f8e1855b8", "size.spacing.1": "3baa08f10ba88ab01eab722ad492456fb0143c0e", "color.transparency.primary-minimal": "89c8374e32620af5cf45723ba649858766fe260c", "color.transparency.primary-subtle": "ed036ee43eb43096442017714a601dfdd31d8b4f", "color.hover.neutral": "c0a3f1cfb270557fa5893b2e38c95b0ea53af591", "color.hover.informative": "bb2bd5598f900b882303f3747116a16afbf31a41", "text.display-1.family": "72cf225dcc8c83fa326797ffed3f066ef950c3e9", "text.display-1.moderate-weight": "9d5dd78df373b95fa87d3a9e759ad8d1bd96a888", "text.display-1.size": "128f0c885be69e5e84c2daa6fbf4ae7d6112c14b", "text.display-1.strong-weight": "a6789cea80437e5ffcd9cf870266a0fd6710a4d8", "text.heading-2.family": "66329fcf8171848c801a716f70c3f1284b929e2f", "text.heading-2.strong-weight": "2fa1522484f6cef082dae53611d8c84c21397cb5", "text.heading-2.size": "3b16e1fab9355cab73e0c4329f7ab267f2f0246c", "text.heading-2.moderate-weight": "39ea74b15d22ed856de3a75be20cbbc6357baf92", "text.heading-1.family": "57d0ad686d01ef4a764111f898c2e23a16e78c1b", "text.heading-1.strong-weight": "c799ec002ade0f817dbd38269f6ccc733b34f5ca", "text.heading-1.moderate-weight": "30d73fb8147c1b0f7c99436043610e90f3f08cad", "text.heading-1.size": "5db4c8f470ec803778874cbe4b9c1021372e0fd1", "text.heading-3.family": "b3fb37674de0a39fb6b82efdddaea5cffbe7290a", "text.heading-3.strong-weight": "c4e2c73e3d52c50085444a04714f84ec4d43839f", "text.heading-3.moderate-weight": "66b794b114af3e674249d207b25608c0b4354dea", "text.heading-3.size": "7cf4ef0096547ab1a96f94e0fdf72fc452f79d9d", "text.body-1.family": "c693e01cde163a8f4cbeb6184df4eb56c1de4e05", "text.body-1.strong-weight": "5e3e9db7875373c9d713c0f02b236423caadc7d7", "text.body-1.size": "11d241478d18e8df47a0848377ec613b1a61473e", "text.body-1.moderate-weight": "db0d96db4853b2daf96b5abb303c03f0492f2b4e", "text.body-3.family": "512173edaa906c26c0d5708b72f89434fd732167", "text.body-3.strong-weight": "337c538bc7227345ce20e60bf08ce2689a777337", "text.body-3.moderate-weight": "9160cb25c57a9b4c69f8b7d74250cd47d936020d", "text.body-3.size": "841c7ffc50847294367e8b95ef0673c7a67a00bd", "text.body-2.family": "3249ac4cdf913f75b5db20f0671721296c3f502b", "text.body-2.strong-weight": "aac7344a81352e151e5830354bb202ba1b5bbb4f", "text.body-2.size": "f898df75b03708ef3512db0155c91b0703b9975b", "text.body-2.moderate-weight": "0c556e3027a5d73b4f5dfbe71bf480e6c5a804e6", "text.caption-1.family": "ae109d080a050af76494e6ea16656a30e9417800", "text.caption-1.size": "a537af8affaeff864b511e906b1ef6a141daff99", "text.caption-1.moderate-weight": "ee594618ddadedbbabda1ed24c591ce3cee07d16", "text.display-1.line-height": "fa94251ab6dc40a1cc97ed9cc1e99604aed2b891", "text.heading-1.line-height": "c3cfacd3f5476898bc002461d8a77cdc48709cd4", "text.heading-2.line-height": "1da619b9d0507c0b1b5489321da01a287cde362b", "text.heading-3.line-height": "1d8449fb4b43ce102f591024657b495fbd25398d", "text.body-1.line-height": "5a1283b8b94d710fe78430083d7bfc2d9b115f4d", "text.body-2.line-height": "79870c89a34f131236b785be450ad3cdd1c9bcdf", "text.body-3.line-height": "f4d272292a3cf17e29e6c5572cf87e63d8e05bda", "text.caption-1.line-height": "49f23923cc145ac329b086a49b4c2c7dc1c6b135", "color.border.company-graph.ubo": "7579cb5a9e70063c539aa15b5cff78a63f95c951", "color.border.company-graph.company": "7c448c763d4d444e8f6fc0f70839a8d61f44f491", "color.border.company-graph.person": "d32c513899756ced1fb997d96db811277ab79238", "color.elevation.small": "60b84d4c0f41286174a494c81acc4fdf9322f602", "color.elevation.medium": "19056134f25e1972547f5cce1069b9f8467697d0", "color.elevation.large": "7266efb5840a78d4d56713121aee4fe35ad42abd", "color.transparency.neutral-moderate": "957c359179047ed067ed61608f9d596023fae6e3", "color.background.dark-minimal": "e9f656a7b50472b5b24bedc8df76519869621c44"}, "$figmaModeId": "5120:2", "$figmaCollectionId": "VariableCollectionId:5120:13788"}, {"id": "2-alias-volksbank", "name": "volksbank", "group": "2-<PERSON><PERSON>", "selectedTokenSets": {"2-Alias/volksbank": "enabled"}, "$figmaStyleReferences": {}, "$figmaVariableReferences": {"color.brand.primary": "7a595493d51bbce68c2256ae0824e9901dab77fc", "color.brand.dark": "d7cebcf0f2cf00b5360f193e1d8a6e0820928502", "color.brand.tertiary": "5f7939a94ef3f8c7baa88be056489189155c40c5", "color.brand.secondary": "6f70e17e8e9d5ca74affa719429be2a25ec7d3f8", "color.surface.primary": "43006e9bc2e917cec736d66a505c3e43e03643ae", "color.surface.secondary": "d620287b5aa9e3f1fccda24f8196563a26fbd75b", "color.surface.hover": "578bcab358b56a13b08643aaf2dfa697615c6d3b", "color.status.success": "9413efdcae8c9609190ec02e96e0a1a80f604928", "color.status.error": "52c63409b2d57fd4173bb8ce66920b54232f7fe5", "color.status.failure": "4d83e43dc9c411168354d0d5b03621038e812dc5", "color.status.warning": "2b6b8362e5de9c92cc086c32b5eb4c0ccbc2268e", "color.advanced-editor.first": "8122ff21744f4bcd501efd19f34b7b37351a101e", "color.advanced-editor.second": "66922e1c92633194341ee145d88ba6aba5f3164b", "color.advanced-editor.third": "9a910566b6d7ddb6e88af9df6a83aa1576f8f278", "color.advanced-editor.fourth": "39f9c9299bb0adcf1e06686c1cdc5238718eabd6", "color.advanced-editor.fifth": "138c4bb13f97596394235c46f66ddcf87056bff6", "color.advanced-editor.sixth": "d869309ca5016e83ea4245382b8aa101cf65b01e", "color.advanced-editor.seventh": "491e67da835cfcef4890fed054b71331b55f0112", "color.advanced-editor.eighth": "8ce46e05757cb4eae271dbbc6297622a8c51730e", "color.status.neutral": "fd76107eab94a41820dbdc6ca1b9061dbf0d7e1a", "color.status.informative": "f0b2fe02f2eb587a97c4d3468eb0d617f7172c1c", "color.surface.dark-strong": "600d29e8ada6303ff94b135d6c95e0694088123d", "color.text.tertiary": "81949ba703642f17e8dc89d873a42f56b4868938", "color.text.primary": "d08e8f3c7c79de97dedd82a96b7689a9c5fc234a", "color.text.interactive": "d94a119c1224e0795facad53f47b33e4801c9cf1", "color.text.light": "b15a65aa4b09782b80db2867621cad91202537ac", "color.text.secondary": "51e5060863602e4d48e0af2bc0199509984b0f4d", "color.surface.tertiary": "27acfe52e842d58ae25e9726ba2aaf1cc6115f5f", "color.surface.dark-subtle": "fa74bcab2fc59cd4b0c202e847bb2e46741ede5a", "color.border.default.primary": "d47f3250ec89f0f08ebad0cdc9ca536b08017164", "color.icons.primary": "11d3797c54235cc4de5e4eca91a582bbc4d161a3", "color.icons.disabled": "3858b9ca49dbb6dc8c0ad0676afd2967471d1dad", "color.border.default.inactive": "5e0d2aac2ffd3fcefc86e7a9f20a4335f6cdec84", "color.border.default.success": "6241b3b62c526509d24723fbbbfb0f5742520955", "color.border.default.error": "10c5752b42c5a95822d10eb74b71e03ce3c4cccf", "color.border.default.hover": "bb19a151787f28faca2dfed901bbf3eb8dd5b274", "color.text.disabled": "faf10cdadc8e0ece4bca186afe314f1a7b5ae76f", "color.text.success": "4102ed6aee5f71e92d253f35c98e445baffa58f7", "color.text.error": "ac945c2f698d057d2ec6f3bad31a5b552dcd7607", "color.icons.success": "807faf2916ea20a88fc1a001bc1ca1f0bc1d5af4", "color.icons.error": "1e5f96144cc642afeed2a4aa344c17ee29fdd3fa", "color.icons.brand-primary": "1ce0fd21ff86fe5e00f781f15a626253ebbb0862", "color.icons.interactive": "0401d0c8674a0c5ca38843fdcabd130a2f1fc4a2", "color.icons.light": "049180a6d182cd45bca8b4a69eeb00e9666036e1", "color.border.default.warning": "00a6d38105c17453e719856c773bc2f3febf40c9", "color.background.primary-strong": "e59c6659fbaf0ee9f2c120a4f2f940b688142a5c", "color.background.secondary-strong": "71fa46a6e2fb4eb7ba169e29e3058e06726f2dfb", "color.background.tertiary-subtle": "d7efc69a77181b07b35ff1eca0ed1ced0acd0986", "color.background.disabled": "ce6c0697645b418d8d656888606ac8c2b8565aee", "color.hover.primary": "c349f0e12c90338f9040d50b668a9b32aaf3276c", "color.hover.tertiary": "eef5af69935fd957411d636f968a2c6f864210f2", "color.hover.attention": "5ac1504be02e195c11da3238fdb2eec80a19a75d", "color.background.dark-strong": "1392680d15dd3c302c2a71ec2039ae289042118e", "color.background.primary-moderate": "d2cfb283ab716d257e237d86c89b48d2fe983aeb", "color.background.secondary-subtle": "ae7c029bf0e3c11b60466059a39eb482849e6823", "color.background.dark-subtle": "a292a2c3ea0eab7c108fa291790699d0b924b1ca", "color.background.warning-strong": "afa449474d3706f2e3c134bdc19a1e9bfd526e23", "color.background.warning-subtle": "2e6df71237f08ea389a3c5fbdaebe5d6a6357b65", "color.background.success-strong": "79e9714a0d52bdc7c0af5ca924a75de81a36fdc1", "color.background.success-subtle": "26ecc99c8a87c3644159dafa396c85173ebda629", "color.background.failure-strong": "352862c27ef4f71ba77f57e29ec071f6d6e69c4c", "color.background.failure-subtle": "744242d2fd69ff4223679fade42c17b6bc655527", "color.background.attention-strong": "ac7b8357be682117f54386a41d0f5fab54afa493", "color.background.attention-subtle": "cfdc830a33d71dd7397bcc3ddcab2f1b6e2b2c3f", "color.text.warning": "8c38e64643394d06cf1456d172f934940ee401d4", "color.icons.tertiary": "f33dd353bb9dc1e48e6480c2d3c3cbf5c27f8a03", "color.background.neutral-minimal": "ce8c021ea5481de71d20740bb5ac2f7329de2a6f", "color.hover.secondary": "f47df59a98210900cb45f9b69f011bd8c889a17d", "color.background.light": "de29e924479bf8b619ab409426e187fe090137d1", "color.border.default.minimal": "b51cb970b5663ed2cea8390e86e3bf94d44a3a09", "color.border.default.interactive": "61ceea6614d2ab0894100161c7275a673bd0ba63", "color.icons.dark": "4fec5ae68afb2ea3c1c312a68e61b38daebe816f", "color.background.tertiary-strong": "05d5dd81737d95d939591842d5a925eafbe00d4f", "color.icons.minimal": "7f6582aa15306f74fa118e88355edffa41d38a30", "color.icons.secondary": "8d25dab8d222bd8912292b1f7b4b4b4ad010b697", "color.background.dark-moderate": "620be1a3abafee4c0c953090341728c61c137476", "color.background.dark-bold": "d4dc16007d56f103153a0ef5bb1a38512753d958", "color.text.minimal": "52706eb6a9b50a242baf57122888cc904aaef875", "color.border.default.light": "387746e7dba9f28c8d38e5d799e7f4672cd7a95f", "color.border.default.strong": "dcbf6b53cc3af79b049ccc356fc6c174901129df", "color.background.primary-minimal": "02ab621f5c1eb082a7786dd5f52871553b56562e", "color.background.secondary-minimal": "94e83de895715c7527953bc7fac64bfeffd5798b", "color.background.tertiary-minimal": "18cae2ee89e3681acab176d53da14fce3cd08692", "color.background.attention-minimal": "46b7298de8e61cc7b65b953e86721ed917768544", "color.background.warning-minimal": "8594b8321a57f72d602b9658a7728254506cf559", "color.background.success-minimal": "d275335e2bb140a3471f36157969dee2b4bc3819", "color.background.failure-minimal": "f4f09882a48b69f8c58664139dddceb7922587f4", "color.icons.warning": "c2ddefe9b96b4bd699afb0812415f7b7e7ab8c21", "size.spacing.none": "a3a0b08a76e0668f329b4ac0e3da8b6f42072fbf", "size.spacing.2": "439440036f5b76d630cede677a3d1c9c873806d7", "size.spacing.4": "d6c617c75b738748a3347ddb14be4c89f52251e1", "size.spacing.6": "9f246fad69de74c423be941317d9c2559c874261", "size.spacing.8": "926ef6756f9321285329aa33fd23fb0ddfb47cdc", "size.spacing.12": "ac3764ceb2d369e082b66180501996262ed7aa17", "size.spacing.16": "4341f29208c64b4cf39e466c74179e84d3c52bd3", "size.spacing.20": "1f7b7e3e2e7de69f892a48927d9af44250da4745", "size.spacing.24": "22ce4195de314151a4db59e5c7ff28204e08379f", "size.spacing.32": "432c70f5de429485ce2b5841820cd3e781ee5fa1", "size.spacing.40": "edd2551767d33f78f0d7e3a32afb55a0d5230c15", "size.spacing.48": "46b39cfec86d7fffc980fea8f0e97162bb5f01e5", "size.spacing.56": "4bf76485136feb0e4bfdaf366d86f32694803fac", "size.spacing.64": "d5376e5a5327f3fa0021f36c93eb6dfe8d70602e", "size.spacing.80": "51164f126877c48023d939d19f365076cec02d29", "size.spacing.120": "1caacf92a91a5e266f964c86a67dd9b13de2ce9a", "size.border.none": "36037156cf600f2b00e86fc8720ab074c3488461", "size.border.s": "4c26fb4430a552867974fa7d6894f659a0ac1170", "size.border.m": "45cd8d9b257639e7c893313c442b3aaf97f35f1a", "size.corner-radius.square": "51a405a88181625daa989830bfe3c1ed9ea41a22", "size.corner-radius.s": "8e2ecc489ddda461f3edd9c60be4245a06a27eb0", "size.corner-radius.m": "cb5d25bdfbef0d98fcc676fd502f32b5dbd6c003", "size.corner-radius.l": "c4dc835b27c13440937ee4a1e3cf80be6e8199a1", "size.corner-radius.round": "362e5b4133cff7eb7de3f6c75d27e5245bf2cc7b", "color.transparency.attention-subtle": "347243bbb7d1718657a3651910d6d15e75245a4f", "color.transparency.attention-minimal": "2d6aa74744b399677fa4b6810602db798a4bac0c", "color.transparency.secondary-subtle": "b3dcbe1312bec8871b63ad3b1d80eaf1725d389b", "color.transparency.secondary-minimal": "bce1f8e125c6a3817ccf571660b6cdeab399fb96", "color.transparency.dark-subtle": "458c8dd24de4743a8f3c2e57a1e55af7d54be8d0", "color.transparency.dark-bold": "ff6b000c845ca8f3a16b7405cb1ad9cfcaa11b88", "color.background.primary-subtle": "2d74a82a1adef82bffd9b9fec5b9d1c96f92c7cc", "color.background.secondary-moderate": "20efc540ce95e8bd6328f28ab5bde864b45784f2", "color.background.tertiary-moderate": "631b3382fa7b7ed48a325252a3d6296bed069be5", "color.background.attention-moderate": "89df70a74e99a721a3fe43e744fd2687ec5c2e8f", "color.background.warning-moderate": "2998cdabd68745ceac2204ef0f70d998b07df08a", "color.background.success-bold": "e11b4dbe3ab8c37979af6ad89126927988b7a0cb", "color.background.failure-moderate": "8681602703555cffa9415b81bb3ae1650b18c094", "color.background.primary-bold": "c59139f0752b50b927de700369949343268b230c", "color.background.secondary-bold": "651cd8dc0c23568d69882c28ed388133821075a7", "color.background.tertiary-bold": "d236ab352f6093d0504cb2a89f2cf733a5a2e4a3", "color.background.attention-bold": "4d97e03a9707af6c6b587bc31b0cb89b042f227d", "color.background.warning-bold": "aa95a901cd10c7ef8c87df1f82193281fdd66c22", "color.background.success-moderate": "0d28059ecff94cce1bd62fb48337475c1a1b2aff", "color.background.failure-bold": "42bd287cea157ec716d32ed57b557dadcc6a2dd2", "size.spacing.18": "ea4451c399d250264f9112ae98f0d18f8e1855b8", "size.spacing.1": "3baa08f10ba88ab01eab722ad492456fb0143c0e", "color.transparency.primary-minimal": "89c8374e32620af5cf45723ba649858766fe260c", "color.transparency.primary-subtle": "ed036ee43eb43096442017714a601dfdd31d8b4f", "color.hover.neutral": "c0a3f1cfb270557fa5893b2e38c95b0ea53af591", "color.hover.informative": "bb2bd5598f900b882303f3747116a16afbf31a41", "text.display-1.family": "72cf225dcc8c83fa326797ffed3f066ef950c3e9", "text.display-1.moderate-weight": "9d5dd78df373b95fa87d3a9e759ad8d1bd96a888", "text.display-1.size": "128f0c885be69e5e84c2daa6fbf4ae7d6112c14b", "text.display-1.strong-weight": "a6789cea80437e5ffcd9cf870266a0fd6710a4d8", "text.heading-2.family": "66329fcf8171848c801a716f70c3f1284b929e2f", "text.heading-2.strong-weight": "2fa1522484f6cef082dae53611d8c84c21397cb5", "text.heading-2.size": "3b16e1fab9355cab73e0c4329f7ab267f2f0246c", "text.heading-2.moderate-weight": "39ea74b15d22ed856de3a75be20cbbc6357baf92", "text.heading-1.family": "57d0ad686d01ef4a764111f898c2e23a16e78c1b", "text.heading-1.strong-weight": "c799ec002ade0f817dbd38269f6ccc733b34f5ca", "text.heading-1.moderate-weight": "30d73fb8147c1b0f7c99436043610e90f3f08cad", "text.heading-1.size": "5db4c8f470ec803778874cbe4b9c1021372e0fd1", "text.heading-3.family": "b3fb37674de0a39fb6b82efdddaea5cffbe7290a", "text.heading-3.strong-weight": "c4e2c73e3d52c50085444a04714f84ec4d43839f", "text.heading-3.moderate-weight": "66b794b114af3e674249d207b25608c0b4354dea", "text.heading-3.size": "7cf4ef0096547ab1a96f94e0fdf72fc452f79d9d", "text.body-1.family": "c693e01cde163a8f4cbeb6184df4eb56c1de4e05", "text.body-1.strong-weight": "5e3e9db7875373c9d713c0f02b236423caadc7d7", "text.body-1.size": "11d241478d18e8df47a0848377ec613b1a61473e", "text.body-1.moderate-weight": "db0d96db4853b2daf96b5abb303c03f0492f2b4e", "text.body-3.family": "512173edaa906c26c0d5708b72f89434fd732167", "text.body-3.strong-weight": "337c538bc7227345ce20e60bf08ce2689a777337", "text.body-3.moderate-weight": "9160cb25c57a9b4c69f8b7d74250cd47d936020d", "text.body-3.size": "841c7ffc50847294367e8b95ef0673c7a67a00bd", "text.body-2.family": "3249ac4cdf913f75b5db20f0671721296c3f502b", "text.body-2.strong-weight": "aac7344a81352e151e5830354bb202ba1b5bbb4f", "text.body-2.size": "f898df75b03708ef3512db0155c91b0703b9975b", "text.body-2.moderate-weight": "0c556e3027a5d73b4f5dfbe71bf480e6c5a804e6", "text.caption-1.family": "ae109d080a050af76494e6ea16656a30e9417800", "text.caption-1.size": "a537af8affaeff864b511e906b1ef6a141daff99", "text.caption-1.moderate-weight": "ee594618ddadedbbabda1ed24c591ce3cee07d16", "text.display-1.line-height": "fa94251ab6dc40a1cc97ed9cc1e99604aed2b891", "text.heading-1.line-height": "c3cfacd3f5476898bc002461d8a77cdc48709cd4", "text.heading-2.line-height": "1da619b9d0507c0b1b5489321da01a287cde362b", "text.heading-3.line-height": "1d8449fb4b43ce102f591024657b495fbd25398d", "text.body-1.line-height": "5a1283b8b94d710fe78430083d7bfc2d9b115f4d", "text.body-2.line-height": "79870c89a34f131236b785be450ad3cdd1c9bcdf", "text.body-3.line-height": "f4d272292a3cf17e29e6c5572cf87e63d8e05bda", "text.caption-1.line-height": "49f23923cc145ac329b086a49b4c2c7dc1c6b135", "color.border.company-graph.ubo": "7579cb5a9e70063c539aa15b5cff78a63f95c951", "color.border.company-graph.company": "7c448c763d4d444e8f6fc0f70839a8d61f44f491", "color.border.company-graph.person": "d32c513899756ced1fb997d96db811277ab79238", "color.elevation.small": "60b84d4c0f41286174a494c81acc4fdf9322f602", "color.elevation.medium": "19056134f25e1972547f5cce1069b9f8467697d0", "color.elevation.large": "7266efb5840a78d4d56713121aee4fe35ad42abd", "color.transparency.neutral-moderate": "957c359179047ed067ed61608f9d596023fae6e3", "color.background.dark-minimal": "e9f656a7b50472b5b24bedc8df76519869621c44"}, "$figmaModeId": "31069:0", "$figmaCollectionId": "VariableCollectionId:5120:13788"}], "$metadata": {"tokenSetOrder": ["1-Base/raw-values", "2-<PERSON><PERSON>/neoshare", "2-Alias/volksbank", "2-<PERSON><PERSON>/sparkasse", "3-Component/neoshare", "3-Component/volksbank", "3-Component/sparkasse"]}}