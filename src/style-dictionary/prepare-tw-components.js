import fs from "fs";
import StyleDictionary from "style-dictionary";
import { logVerbosityLevels } from "style-dictionary/enums";
import { Project, SyntaxKind } from "ts-morph";
import "./tokens-filters.js";
import "./token-transformers.js";
import "./tw-components-formatter.js";

// Read the tokens.json file as before
const tokensData = fs.readFileSync("tokens.json", "utf8");
const tokens = JSON.parse(tokensData);

// Filter out themes that should not be built (i.e. "All")
const themes = tokens.$themes.filter((theme) => theme.name !== "All");

const firstLevelTokens = tokens["1-Base/raw-values"];

const secondLevelTokens = tokens[themes[0].group + "/" + themes[0].name];
const sdTwComponents = new StyleDictionary({
  tokens: {
    ...firstLevelTokens,
    ...secondLevelTokens,
  },
  options: {
    log: {
      verbosity: logVerbosityLevels.verbose,
    },
  },
  platforms: {
    tailwindComponents: {
      buildPath: "props/",
      transformGroup: "js",
      transforms: ["name/kebab"],

      files: [
        {
          destination: `tw-components-props.js`,
          format: "javascript/esm.tw-components-props",
          filter: "filterTextTokens",
        },
      ],
    },
  },
});

await sdTwComponents.buildAllPlatforms();

// Create a new ts-morph project.
const project = new Project();

// Load the source file you want to refactor.
const sourceFile = project.addSourceFileAtPath("props/tw-components-props.js");

// Recursively visit each descendant node.
sourceFile.forEachDescendant((node) => {
  // We are interested in string literals.
  if (node.getKind() === SyntaxKind.StringLiteral) {
    // Get the literal text (without quotes).
    const stringValue = node.getLiteralText();
    // Check if it matches the pattern "{{...}}"
    const regex = /^{{\s*(.+?)\s*}}$/;
    const match = regex.exec(stringValue);
    if (match) {
      const innerValue = match[1];
      // Replace with `theme('innerValue')`
      // Note: This replacement is done as text so that the call expression is injected.
      node.replaceWithText(`theme('${innerValue}')`);
    }
  }
});

// Save the changes back to disk.
await sourceFile.save();
