import StyleDictionary from "style-dictionary";
import _ from "lodash";

function getTextComponents(token) {
  if (token.attributes.item.endsWith("family")) {
    return { fontFamily: `{{fontFamily.${token.name}}}` };
  }
  if (token.attributes.item.endsWith("weight")) {
    return { fontWeight: `{{fontWeight.${token.name}}}` };
  }
  if (token.attributes.item.endsWith("line-height")) {
    return { lineHeight: `{{lineHeight.${token.name}}}` };
  }
  if (token.attributes.item.endsWith("size")) {
    return { fontSize: `{{fontSize.${token.name}}}` };
  }
}

function tailwindConfigFormatter({ dictionary, options, file }) {
  // Step 1: Initialize empty component objects for every font-weight.
  const components = dictionary.allTokens.reduce((acc, token) => {
    if (token.attributes.item.endsWith("weight")) {
      const weightType = token.attributes.item.replace("-weight", "");
      const componentType = token.attributes.type;
      const category = token.attributes.category;

      const formattedComponentName = `.${category}-${componentType}-${weightType}`;
      acc[formattedComponentName] = {};
    }
    return acc;
  }, {});

  // Step 2: Merge token properties into the appropriate component objects.
  dictionary.allTokens.forEach((token) => {
    const textComponent = getTextComponents(token);

    const componentType = token.attributes.type;
    const category = token.attributes.category;
    if (token.attributes.item.endsWith("weight")) {
      // Directly add to the component that matches the token's weight.
      const weightType = token.attributes.item.replace("-weight", "");
      const componentName = `.${category}-${componentType}-${weightType}`;

      components[componentName] = {
        ...components[componentName],
        ...textComponent,
      };
    } else {
      // Otherwise, add the token to all components that start with the same type.
      Object.keys(components)
        .filter((key) => key.startsWith(`.${category}-${componentType}`))
        .forEach((componentName) => {
          components[componentName] = {
            ...components[componentName],
            ...textComponent,
          };
        });
    }
  });

  // Wrap the computed objects into an ES module export with the getComponentsProps method.
  return `export default { \n getComponentsProps: (theme) => (${JSON.stringify(
    components,
    null,
    2
  )})};`;
}

// Register the custom format with Style Dictionary.
StyleDictionary.registerFormat({
  name: "javascript/esm.tw-components-props",
  format: tailwindConfigFormatter,
});
