import StyleDictionary from "style-dictionary";
import { toRem } from "../utils/to-rem.js";
import _ from "lodash";
/**
 * Processes tokens that relate to text styles.
 * Depending on the token name (e.g., ending with "family", "weight", "line height", "size"),
 * it assigns the token value to the appropriate TailwindCSS theme property in the result object.
 *
 * @param {object} token - The token object from Style Dictionary.
 * @param {object} result - The accumulator object where the formatted tokens are stored.
 * @returns {object} - The updated result object with the new token applied.
 */
function getTextTokens(token, result) {
  if (token.attributes.item.endsWith("family")) {
    result.fontFamily[token.name] = [token.value];
    return result;
  }
  if (token.attributes.item.endsWith("weight")) {
    result.fontWeight[token.name] = token.value;
    return result;
  }
  if (token.attributes.item.endsWith("line-height")) {
    result.lineHeight[token.name] = token.value;
    return result;
  }
  if (token.attributes.item.endsWith("size")) {
    result.fontSize[token.name] = [token.value];
    return result;
  }
  return result;
}

/**
 * Processes tokens that relate to sizes (spacing, borders, and corner radii).
 * Depending on the token's attribute type (e.g., "Spacing", "Border", "Corner Radius"),
 * it assigns the token value to the corresponding TailwindCSS theme property in the result object.
 *
 * @param {object} token - The token object from Style Dictionary.
 * @param {object} result - The accumulator object where formatted tokens are stored.
 * @returns {object} - The updated result object with the new token applied.
 */
function getSizeTokens(token, result) {
  if (token.attributes.type === "spacing") {
    result.spacing[token.name] = [token.value];
    return result;
  }
  if (token.attributes.type === "border") {
    result.borderWidth[token.name] = [token.value];
    return result;
  }
  if (token.attributes.type === "corner-radius") {
    result.borderRadius[token.name] = [token.value];
    return result;
  }
  return result;
}

/**
 * Custom formatter function for TailwindCSS theme configuration.
 * It iterates over all tokens in the dictionary and organizes them into a structured
 * theme properties object based on the token categories.
 * - "COLOUR FILLS" tokens are added to the colors property.
 * - "TEXT" tokens are passed to getTextTokens for additional processing.
 * - "SIZES" tokens are passed to getSizeTokens for additional processing.
 *
 * The final output is returned as a string in ES module export syntax.
 *
 * @param {object} params - The parameter object.
 * @param {object} params.dictionary - The Style Dictionary dictionary object containing tokens.
 * @returns {string} - The resulting theme properties as an exported ES module string.
 */
function tailwindConfigFormatter({ dictionary, options, file }) {
  const themeProperties = dictionary.allTokens.reduce(
    (result, token) => {
      if (
        token.attributes.category === "color" ||
        token.attributes.item === "color" ||
        token.attributes.subitem === "color"
      ) {
        result.colors[token.name] = token.value;
        return result;
      }
      if (token.attributes.category === "text") {
        return getTextTokens(token, result);
      }
      if (token.attributes.category === "size") {
        return getSizeTokens(token, result);
      }
      if (token.type === "boxShadow") {
        const { x, y, blur, spread, color } = token.value;

        result.boxShadow[token.name] =
          `${toRem(x)} ${toRem(y)} ${toRem(blur)} ${toRem(spread)} ${color}`;
        return result;
      }
      if (token.attributes.category === "ai-gradient") {
        result.backgroundImage[token.name] = token.value;
        return result;
      }
      return result;
    },
    {
      colors: {},
      fontFamily: {},
      fontWeight: {},
      lineHeight: {},
      fontSize: {},
      borderWidth: {},
      borderRadius: {},
      spacing: {},
      backgroundImage: {},
      boxShadow: {},
    }
  );
  // Return a JavaScript ES module export containing the TailwindCSS theme properties.
  return `export default ${JSON.stringify(themeProperties, null, 2)};`;
}

// Register the custom format with Style Dictionary.
StyleDictionary.registerFormat({
  name: "javascript/esm.tw-props",
  format: tailwindConfigFormatter,
});
