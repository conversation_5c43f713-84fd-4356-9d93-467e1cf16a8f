import StyleDictionary from "style-dictionary";
import { transformTypes } from "style-dictionary/enums";
import { baseTokensFilter } from "../utils/base-tokens-filter.js";
import _ from "lodash";

StyleDictionary.registerTransform({
  name: "name/nestedToken",
  type: transformTypes.name,
  transform: function (token, config, options) {
    const tokenName = token.path.slice().pop().toLowerCase();
    return tokenName;
  },
  filter: baseTokensFilter,
});

StyleDictionary.registerTransform({
  name: "attribute/theme",
  type: transformTypes.attribute,
  transform: function (token, { theme }, options) {
    return { theme };
  },
  filter: baseTokensFilter,
});

// TODO: font-weight should come as type "number"
StyleDictionary.registerTransform({
  name: "size/fontWeight",
  type: transformTypes.value,
  filter: function (token) {
    return (
      token.attributes.item === "weight" &&
      token.attributes.category !== "base-colors"
    );
  },
  transform: function (token) {
    // Reset font-weight values to numbers
    return parseFloat(token.value) * 10;
  },
});
