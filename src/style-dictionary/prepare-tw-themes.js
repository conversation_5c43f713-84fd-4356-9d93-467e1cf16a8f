import fs from "fs";
import StyleDictionary from "style-dictionary";
import { logVerbosityLevels } from "style-dictionary/enums";
import "./tokens-filters.js";
import "./tw-themes-formatter.js";
import "./token-transformers.js";

// Read the tokens.json file as before
const tokensData = fs.readFileSync("tokens.json", "utf8");
const tokens = JSON.parse(tokensData);

// Filter out themes that should not be built (i.e. "All")
const rawThemes = tokens.$themes.filter(
  (theme) => theme.id !== "1-base-raw-values"
);

const firstLevelTokens = tokens["1-Base/raw-values"];

// Separate alias and component entries
const aliasEntries = rawThemes.filter((item) => item.group === "2-Alias");
const componentEntries = rawThemes.filter(
  (item) => item.group === "3-Component"
);

// Merge matching component tokens into alias entries
const themes = aliasEntries.map((alias) => {
  const component = componentEntries.find((c) => c.name === alias.name);
  if (component) {
    return {
      ...alias,
      selectedTokenSets: {
        ...alias.selectedTokenSets,
        ...component.selectedTokenSets,
      },
    };
  }
  return alias;
});

for await (const theme of themes) {
  const secondLevelTokens = Object.keys(theme.selectedTokenSets).reduce(
    (res, setName) => {
      return {
        ...res,
        ...tokens[setName],
      };
    },
    {}
  );

  const sd = new StyleDictionary({
    tokens: {
      ...firstLevelTokens,
      ...secondLevelTokens,
    },
    options: {
      log: {
        verbosity: logVerbosityLevels.verbose,
      },
    },
    platforms: {
      tailwind: {
        theme: theme.name,
        transformGroup: "js",
        buildPath: "props/",
        basePxFontSize: 10,
        // name/nestedToken
        transforms: [
          "name/kebab",
          "attribute/theme",
          "size/pxToRem",
          "size/fontWeight",
        ],
        files: [
          {
            destination: `tw-theme-${theme.name}-props.js`,
            format: "javascript/esm.tw-props",
            filter: "filterBaseTokens",
          },
        ],
      },
    },
  });

  // Build the Tailwind platform.
  await sd.buildPlatform("tailwind");
}
