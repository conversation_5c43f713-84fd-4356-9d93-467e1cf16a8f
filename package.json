{"name": "@fincloud/lib-ui-tokens", "version": "1.0.42", "type": "module", "description": "", "scripts": {"build:themes": "node src/style-dictionary/prepare-tw-themes.js", "build:components": "node src/style-dictionary/prepare-tw-components.js"}, "publishConfig": {"@fincloud:registry": "https://nexus-v2.neo.loan/repository/npm/", "access": "restricted"}, "repository": {"type": "git", "url": "https://git.neo.loan/fincloud/lib-ui-token.git"}, "author": "Neoshare", "main": "./index.js", "exports": {".": "./props/index.js", "./*": "./props/*"}, "license": "", "dependencies": {"requireindex": "1.2.0", "lodash": "^4.17.21"}, "devDependencies": {"style-dictionary": "4.4.0", "ts-morph": "25.0.1"}}