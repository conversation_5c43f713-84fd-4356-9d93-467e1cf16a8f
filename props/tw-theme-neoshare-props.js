export default {
  "colors": {
    "color-brand-primary": "#6ae562",
    "color-brand-dark": "#26283e",
    "color-brand-tertiary": "#42526e",
    "color-brand-secondary": "#060fd0",
    "color-surface-primary": "#ffffff",
    "color-surface-secondary": "#f8f8f8",
    "color-surface-hover": "#ececec",
    "color-surface-dark-strong": "#26283e",
    "color-surface-tertiary": "#f3f3f3",
    "color-surface-dark-subtle": "#42526e",
    "color-status-success": "#00675d",
    "color-status-error": "#ff6157",
    "color-status-failure": "#e3c902",
    "color-status-warning": "#ff990b",
    "color-status-neutral": "#42526e",
    "color-status-informative": "#060fd0",
    "color-advanced-editor-first": "#e0ccdc",
    "color-advanced-editor-second": "#f5cef8",
    "color-advanced-editor-third": "#bedcff",
    "color-advanced-editor-fourth": "#def6fe",
    "color-advanced-editor-fifth": "#a1e5a3",
    "color-advanced-editor-sixth": "#e1ffaf",
    "color-advanced-editor-seventh": "#d89292",
    "color-advanced-editor-eighth": "#ffd3aa",
    "color-text-tertiary": "#7d7e8b",
    "color-text-primary": "#26283e",
    "color-text-interactive": "#060fd0",
    "color-text-light": "#ffffff",
    "color-text-secondary": "#42526e",
    "color-text-disabled": "#a8a9b2",
    "color-text-success": "#00675d",
    "color-text-error": "#ff6157",
    "color-text-warning": "#ff990b",
    "color-text-minimal": "#d4d4d8",
    "color-border-default-primary": "#d4d4d8",
    "color-border-default-inactive": "#bebec5",
    "color-border-default-success": "#00675d",
    "color-border-default-error": "#ff6157",
    "color-border-default-hover": "#26283e",
    "color-border-default-warning": "#ff990b",
    "color-border-default-minimal": "#e9e9ec",
    "color-border-default-interactive": "#060fd0",
    "color-border-default-light": "#ffffff",
    "color-border-default-strong": "#92939e",
    "color-border-company-graph-ubo": "#8287e7",
    "color-border-company-graph-company": "#80b3ae",
    "color-border-company-graph-person": "#ffcc85",
    "color-icons-primary": "#26283e",
    "color-icons-disabled": "#a8a9b2",
    "color-icons-success": "#00675d",
    "color-icons-error": "#ff6157",
    "color-icons-brand-primary": "#6ae562",
    "color-icons-interactive": "#060fd0",
    "color-icons-light": "#ffffff",
    "color-icons-tertiary": "#92939e",
    "color-icons-dark": "#676978",
    "color-icons-minimal": "#bebec5",
    "color-icons-secondary": "#42526e",
    "color-icons-warning": "#ff990b",
    "color-background-primary-strong": "#6ae562",
    "color-background-secondary-strong": "#060fd0",
    "color-background-tertiary-subtle": "#d9dce2",
    "color-background-disabled": "#e9e9ec",
    "color-background-dark-strong": "#26283e",
    "color-background-primary-moderate": "#b4f2b0",
    "color-background-secondary-subtle": "#cdcff6",
    "color-background-dark-subtle": "#d4d4d8",
    "color-background-warning-strong": "#ff990b",
    "color-background-warning-subtle": "#ffebce",
    "color-background-success-strong": "#00675d",
    "color-background-success-subtle": "#cce1df",
    "color-background-failure-strong": "#e3c902",
    "color-background-failure-subtle": "#f9f4cc",
    "color-background-attention-strong": "#ff6157",
    "color-background-attention-subtle": "#ffdfdd",
    "color-background-neutral-minimal": "#f8f8f8",
    "color-background-light": "#ffffff",
    "color-background-tertiary-strong": "#42526e",
    "color-background-dark-moderate": "#a8a9b2",
    "color-background-dark-bold": "#676978",
    "color-background-primary-minimal": "#f0fcef",
    "color-background-secondary-minimal": "#e6e7fa",
    "color-background-tertiary-minimal": "#eceef0",
    "color-background-attention-minimal": "#ffefee",
    "color-background-warning-minimal": "#fff5e7",
    "color-background-success-minimal": "#e5f0ef",
    "color-background-failure-minimal": "#fcfae6",
    "color-background-primary-subtle": "#e1fae0",
    "color-background-secondary-moderate": "#8287e7",
    "color-background-tertiary-moderate": "#a0a8b6",
    "color-background-attention-moderate": "#ffb0ab",
    "color-background-warning-moderate": "#ffcc85",
    "color-background-success-bold": "#33857d",
    "color-background-failure-moderate": "#f1e480",
    "color-background-primary-bold": "#88ea81",
    "color-background-secondary-bold": "#383fd9",
    "color-background-tertiary-bold": "#68758b",
    "color-background-attention-bold": "#ff8179",
    "color-background-warning-bold": "#ffad3c",
    "color-background-success-moderate": "#80b3ae",
    "color-background-failure-bold": "#e9d435",
    "color-background-dark-minimal": "#e9e9ec",
    "color-hover-primary": "#5dca56",
    "color-hover-tertiary": "#d9dce2",
    "color-hover-attention": "#e5574e",
    "color-hover-secondary": "#26283e",
    "color-hover-neutral": "#ececec",
    "color-hover-informative": "#e6e7fa",
    "color-transparency-attention-subtle": "#ff615733",
    "color-transparency-attention-minimal": "#ff61571a",
    "color-transparency-secondary-subtle": "#060fd033",
    "color-transparency-secondary-minimal": "#060fd01a",
    "color-transparency-dark-subtle": "#26283e33",
    "color-transparency-dark-bold": "#26283eb3",
    "color-transparency-primary-minimal": "#6ae5621a",
    "color-transparency-primary-subtle": "#6ae56233",
    "color-transparency-neutral-moderate": "#f8f8f866",
    "color-elevation-small": "#26283e26",
    "color-elevation-medium": "#26283e26",
    "color-elevation-large": "#26283e33",
    "buttons-main-set-primary-color-background-default": "#6ae562",
    "buttons-main-set-primary-color-background-hover": "#5dca56",
    "buttons-main-set-primary-color-background-disabled": "#e9e9ec",
    "buttons-main-set-primary-color-background-attention": "#ff6157",
    "buttons-main-set-primary-color-background-attention-hover": "#e5574e",
    "buttons-main-set-primary-color-label-default": "#26283e",
    "buttons-main-set-primary-color-label-hover": "#26283e",
    "buttons-main-set-primary-color-label-disabled": "#a8a9b2",
    "buttons-main-set-primary-color-label-attention": "#ffffff",
    "buttons-main-set-primary-color-label-attention-hover": "#ffffff",
    "buttons-main-set-primary-color-icon-default": "#26283e",
    "buttons-main-set-primary-color-icon-hover": "#26283e",
    "buttons-main-set-primary-color-icon-disabled": "#a8a9b2",
    "buttons-main-set-primary-color-icon-attention": "#ffffff",
    "buttons-main-set-primary-color-icon-attention-hover": "#ffffff",
    "buttons-main-set-secondary-color-background-attention-hover": "#ffefee",
    "buttons-main-set-secondary-color-background-hover": "#e6e7fa",
    "buttons-main-set-secondary-color-border-attention-hover": "#ff6157",
    "buttons-main-set-secondary-color-border-attention": "#ff6157",
    "buttons-main-set-secondary-color-border-disabled": "#bebec5",
    "buttons-main-set-secondary-color-border-hover": "#060fd0",
    "buttons-main-set-secondary-color-border-default": "#060fd0",
    "buttons-main-set-secondary-color-label-attention-hover": "#ff6157",
    "buttons-main-set-secondary-color-label-attention": "#ff6157",
    "buttons-main-set-secondary-color-label-disabled": "#a8a9b2",
    "buttons-main-set-secondary-color-label-hover": "#060fd0",
    "buttons-main-set-secondary-color-label-default": "#060fd0",
    "buttons-main-set-secondary-color-icon-default": "#060fd0",
    "buttons-main-set-secondary-color-icon-hover": "#060fd0",
    "buttons-main-set-secondary-color-icon-disabled": "#a8a9b2",
    "buttons-main-set-secondary-color-icon-attention": "#ff6157",
    "buttons-main-set-secondary-color-icon-attention-hover": "#ff6157",
    "buttons-main-set-informative-color-background-default": "#f8f8f8",
    "buttons-main-set-informative-color-background-hover": "#ececec",
    "buttons-main-set-informative-color-background-disabled": "#e9e9ec",
    "buttons-main-set-informative-color-background-attention": "#ffefee",
    "buttons-main-set-informative-color-background-attention-hover": "#ffdfdd",
    "buttons-main-set-informative-color-label-default": "#26283e",
    "buttons-main-set-informative-color-label-hover": "#26283e",
    "buttons-main-set-informative-color-label-disabled": "#a8a9b2",
    "buttons-main-set-informative-color-label-attention": "#ff6157",
    "buttons-main-set-informative-color-label-attention-hover": "#ff6157",
    "buttons-main-set-informative-color-icon-default": "#26283e",
    "buttons-main-set-informative-color-icon-hover": "#26283e",
    "buttons-main-set-informative-color-icon-disabled": "#a8a9b2",
    "buttons-main-set-informative-color-icon-attention": "#ff6157",
    "buttons-main-set-informative-color-icon-attention-hover": "#ff6157",
    "buttons-main-set-stealth-color-background-hover": "#e6e7fa",
    "buttons-main-set-stealth-color-background-attention-hover": "#ffefee",
    "buttons-main-set-stealth-color-label-default": "#060fd0",
    "buttons-main-set-stealth-color-label-hover": "#060fd0",
    "buttons-main-set-stealth-color-label-disabled": "#a8a9b2",
    "buttons-main-set-stealth-color-label-attention": "#ff6157",
    "buttons-main-set-stealth-color-label-attention-hover": "#ff6157",
    "buttons-main-set-stealth-color-icon-default": "#060fd0",
    "buttons-main-set-stealth-color-icon-hover": "#060fd0",
    "buttons-main-set-stealth-color-icon-disabled": "#a8a9b2",
    "buttons-main-set-stealth-color-icon-attention": "#ff6157",
    "buttons-main-set-stealth-color-icon-attention-hover": "#ff6157",
    "buttons-icon-only-primary-color-background-default": "#6ae562",
    "buttons-icon-only-primary-color-background-hover": "#5dca56",
    "buttons-icon-only-primary-color-background-disabled": "#e9e9ec",
    "buttons-icon-only-primary-color-background-attention": "#ff6157",
    "buttons-icon-only-primary-color-background-attention-hover": "#e5574e",
    "buttons-icon-only-primary-color-icon-default": "#26283e",
    "buttons-icon-only-primary-color-icon-hover": "#26283e",
    "buttons-icon-only-primary-color-icon-disabled": "#a8a9b2",
    "buttons-icon-only-primary-color-icon-attention": "#ffffff",
    "buttons-icon-only-primary-color-icon-attention-hover": "#ffffff",
    "buttons-icon-only-informative-color-background-default": "#f8f8f8",
    "buttons-icon-only-informative-color-background-hover": "#ececec",
    "buttons-icon-only-informative-color-background-disabled": "#e9e9ec",
    "buttons-icon-only-informative-color-background-attention": "#ffefee",
    "buttons-icon-only-informative-color-background-attention-hover": "#ffdfdd",
    "buttons-icon-only-informative-color-icon-default": "#26283e",
    "buttons-icon-only-informative-color-icon-hover": "#26283e",
    "buttons-icon-only-informative-color-icon-disabled": "#a8a9b2",
    "buttons-icon-only-informative-color-icon-attention": "#ff6157",
    "buttons-icon-only-informative-color-icon-attention-hover": "#ff6157",
    "buttons-icon-only-secondary-color-background-hover": "#e6e7fa",
    "buttons-icon-only-secondary-color-background-attention-hover": "#ffefee",
    "buttons-icon-only-secondary-color-border-default": "#060fd0",
    "buttons-icon-only-secondary-color-border-hover": "#060fd0",
    "buttons-icon-only-secondary-color-border-disabled": "#bebec5",
    "buttons-icon-only-secondary-color-border-attention": "#ff6157",
    "buttons-icon-only-secondary-color-border-attention-hover": "#ff6157",
    "buttons-icon-only-secondary-color-icon-default": "#060fd0",
    "buttons-icon-only-secondary-color-icon-hover": "#060fd0",
    "buttons-icon-only-secondary-color-icon-disabled": "#a8a9b2",
    "buttons-icon-only-secondary-color-icon-attention": "#ff6157",
    "buttons-icon-only-secondary-color-icon-attention-hover": "#ff6157",
    "buttons-icon-only-stealth-color-background-hover": "#e6e7fa",
    "buttons-icon-only-stealth-color-background-attention-hover": "#ffefee",
    "buttons-icon-only-stealth-color-icon-default": "#060fd0",
    "buttons-icon-only-stealth-color-icon-hover": "#060fd0",
    "buttons-icon-only-stealth-color-icon-disabled": "#a8a9b2",
    "buttons-icon-only-stealth-color-icon-attention": "#ff6157",
    "buttons-icon-only-stealth-color-icon-attention-hover": "#ff6157",
    "buttons-fab-color-background-default": "#ffffff",
    "buttons-fab-color-background-hover": "#e6e7fa",
    "buttons-fab-color-icon-default": "#060fd0",
    "buttons-fab-color-icon-hover": "#060fd0",
    "buttons-split-primary-color-background-default": "#6ae562",
    "buttons-split-primary-color-background-hover": "#5dca56",
    "buttons-split-primary-color-background-disabled": "#e9e9ec",
    "buttons-split-primary-color-background-attention": "#ff6157",
    "buttons-split-primary-color-background-attention-hover": "#e5574e",
    "buttons-split-primary-color-label-default": "#26283e",
    "buttons-split-primary-color-label-hover": "#26283e",
    "buttons-split-primary-color-label-disabled": "#a8a9b2",
    "buttons-split-primary-color-label-attention": "#ffffff",
    "buttons-split-primary-color-label-attention-hover": "#ffffff",
    "buttons-split-primary-color-icon-default": "#26283e",
    "buttons-split-primary-color-icon-hover": "#26283e",
    "buttons-split-primary-color-icon-disabled": "#a8a9b2",
    "buttons-split-primary-color-icon-attention": "#ffffff",
    "buttons-split-primary-color-icon-attention-hover": "#ffffff",
    "buttons-split-primary-color-separator-default": "#5dca56",
    "buttons-split-primary-color-separator-hover": "#5dca56",
    "buttons-split-primary-color-separator-disabled": "#bebec5",
    "buttons-split-primary-color-separator-attention": "#e5574e",
    "buttons-split-primary-color-separator-attention-hover": "#e5574e",
    "buttons-split-secondary-color-background-hover": "#e6e7fa",
    "buttons-split-secondary-color-background-attention-hover": "#ffefee",
    "buttons-split-secondary-color-border-default": "#060fd0",
    "buttons-split-secondary-color-border-hover": "#060fd0",
    "buttons-split-secondary-color-border-disabled": "#bebec5",
    "buttons-split-secondary-color-border-attention": "#ff6157",
    "buttons-split-secondary-color-border-attention-hover": "#ff6157",
    "buttons-split-secondary-color-label-default": "#060fd0",
    "buttons-split-secondary-color-label-hover": "#060fd0",
    "buttons-split-secondary-color-label-disabled": "#a8a9b2",
    "buttons-split-secondary-color-label-attention": "#ff6157",
    "buttons-split-secondary-color-label-attention-hover": "#ff6157",
    "buttons-split-secondary-color-icon-default": "#060fd0",
    "buttons-split-secondary-color-icon-hover": "#060fd0",
    "buttons-split-secondary-color-icon-disabled": "#a8a9b2",
    "buttons-split-secondary-color-icon-attention": "#ff6157",
    "buttons-split-secondary-color-icon-attention-hover": "#ff6157",
    "buttons-split-secondary-color-separator-default": "#060fd0",
    "buttons-split-secondary-color-separator-hover": "#060fd0",
    "buttons-split-secondary-color-separator-disabled": "#a8a9b2",
    "buttons-split-secondary-color-separator-attention": "#ff6157",
    "buttons-split-secondary-color-separator-attention-hover": "#ff6157",
    "buttons-split-informative-color-background-default": "#e9e9ec",
    "buttons-split-informative-color-background-hover": "#ececec",
    "buttons-split-informative-color-background-disabled": "#e9e9ec",
    "buttons-split-informative-color-background-attention": "#ffefee",
    "buttons-split-informative-color-background-attention-hover": "#ffdfdd",
    "buttons-split-informative-color-label-default": "#26283e",
    "buttons-split-informative-color-label-hover": "#26283e",
    "buttons-split-informative-color-label-disabled": "#a8a9b2",
    "buttons-split-informative-color-label-attention": "#ff6157",
    "buttons-split-informative-color-label-attention-hover": "#ff6157",
    "buttons-split-informative-color-icon-default": "#26283e",
    "buttons-split-informative-color-icon-hover": "#26283e",
    "buttons-split-informative-color-icon-disabled": "#a8a9b2",
    "buttons-split-informative-color-icon-attention": "#ff6157",
    "buttons-split-informative-color-icon-attention-hover": "#ff6157",
    "buttons-split-informative-color-separator-default": "#26283e",
    "buttons-split-informative-color-separator-hover": "#26283e",
    "buttons-split-informative-color-separator-disabled": "#a8a9b2",
    "buttons-split-informative-color-separator-attention": "#ff6157",
    "buttons-split-informative-color-separator-attention-hover": "#ff6157",
    "buttons-action-primary-color-background-hover": "#ececec",
    "buttons-action-primary-color-background-attention-hover": "#ffefee",
    "buttons-action-primary-color-icon-default": "#26283e",
    "buttons-action-primary-color-icon-hover": "#26283e",
    "buttons-action-primary-color-icon-disabled": "#a8a9b2",
    "buttons-action-primary-color-icon-attention": "#ff6157",
    "buttons-action-primary-color-icon-attention-hover": "#ff6157",
    "buttons-action-informative-color-background-hover": "#e6e7fa",
    "buttons-action-informative-color-background-attention-hover": "#ffefee",
    "buttons-action-informative-color-icon-default": "#060fd0",
    "buttons-action-informative-color-icon-hover": "#060fd0",
    "buttons-action-informative-color-icon-disabled": "#a8a9b2",
    "buttons-action-informative-color-icon-attention": "#ff6157",
    "buttons-action-informative-color-icon-attention-hover": "#ff6157",
    "buttons-action-tertiary-color-background-hover": "#ececec",
    "buttons-action-tertiary-color-background-attention-hover": "#ffefee",
    "buttons-action-tertiary-color-icon-default": "#92939e",
    "buttons-action-tertiary-color-icon-hover": "#92939e",
    "buttons-action-tertiary-color-icon-disabled": "#a8a9b2",
    "buttons-action-tertiary-color-icon-attention": "#ff6157",
    "buttons-action-tertiary-color-icon-attention-hover": "#ff6157",
    "illustrations-business-type-color-primary": "#6ae562",
    "illustrations-business-type-color-secondary": "#42526e",
    "illustrations-business-type-color-light": "#ffffff"
  },
  "fontFamily": {
    "text-display-1-family": [
      "Chopin"
    ],
    "text-heading-2-family": [
      "Chopin"
    ],
    "text-heading-1-family": [
      "Chopin"
    ],
    "text-heading-3-family": [
      "Chopin"
    ],
    "text-body-1-family": [
      "Chopin"
    ],
    "text-body-3-family": [
      "Chopin"
    ],
    "text-body-2-family": [
      "Chopin"
    ],
    "text-caption-1-family": [
      "Chopin"
    ]
  },
  "fontWeight": {
    "text-display-1-moderate-weight": 500,
    "text-display-1-strong-weight": 600,
    "text-heading-2-strong-weight": 600,
    "text-heading-2-moderate-weight": 500,
    "text-heading-1-strong-weight": 600,
    "text-heading-1-moderate-weight": 500,
    "text-heading-3-strong-weight": 600,
    "text-heading-3-moderate-weight": 500,
    "text-body-1-strong-weight": 600,
    "text-body-1-moderate-weight": 500,
    "text-body-3-strong-weight": 600,
    "text-body-3-moderate-weight": 500,
    "text-body-2-strong-weight": 600,
    "text-body-2-moderate-weight": 500,
    "text-caption-1-moderate-weight": 500
  },
  "lineHeight": {
    "text-display-1-line-height": "7.2rem",
    "text-heading-2-line-height": "3.6rem",
    "text-heading-1-line-height": "4.8rem",
    "text-heading-3-line-height": "3rem",
    "text-body-1-line-height": "2.4rem",
    "text-body-3-line-height": "1.8rem",
    "text-body-2-line-height": "2rem",
    "text-caption-1-line-height": "1.6rem"
  },
  "fontSize": {
    "text-display-1-size": [
      "4.8rem"
    ],
    "text-heading-2-size": [
      "2.4rem"
    ],
    "text-heading-1-size": [
      "3rem"
    ],
    "text-heading-3-size": [
      "2rem"
    ],
    "text-body-1-size": [
      "1.6rem"
    ],
    "text-body-3-size": [
      "1.2rem"
    ],
    "text-body-2-size": [
      "1.4rem"
    ],
    "text-caption-1-size": [
      "1rem"
    ]
  },
  "borderWidth": {
    "size-border-none": [
      "0"
    ],
    "size-border-s": [
      "0.1rem"
    ],
    "size-border-m": [
      "0.2rem"
    ]
  },
  "borderRadius": {
    "size-corner-radius-square": [
      "0"
    ],
    "size-corner-radius-s": [
      "0.4rem"
    ],
    "size-corner-radius-m": [
      "0.8rem"
    ],
    "size-corner-radius-l": [
      "1.2rem"
    ],
    "size-corner-radius-round": [
      "999.9rem"
    ]
  },
  "spacing": {
    "size-spacing-1": [
      "0.1rem"
    ],
    "size-spacing-2": [
      "0.2rem"
    ],
    "size-spacing-4": [
      "0.4rem"
    ],
    "size-spacing-6": [
      "0.6rem"
    ],
    "size-spacing-8": [
      "0.8rem"
    ],
    "size-spacing-12": [
      "1.2rem"
    ],
    "size-spacing-16": [
      "1.6rem"
    ],
    "size-spacing-18": [
      "1.8rem"
    ],
    "size-spacing-20": [
      "2rem"
    ],
    "size-spacing-24": [
      "2.4rem"
    ],
    "size-spacing-32": [
      "3.2rem"
    ],
    "size-spacing-40": [
      "4rem"
    ],
    "size-spacing-48": [
      "4.8rem"
    ],
    "size-spacing-56": [
      "5.6rem"
    ],
    "size-spacing-64": [
      "6.4rem"
    ],
    "size-spacing-80": [
      "8rem"
    ],
    "size-spacing-120": [
      "12rem"
    ],
    "size-spacing-none": [
      "0"
    ]
  },
  "backgroundImage": {},
  "boxShadow": {}
};