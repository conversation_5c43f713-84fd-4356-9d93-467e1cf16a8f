export default { 
 getComponentsProps: (theme) => ({
  ".text-display-1-moderate": {
    "fontFamily": theme('fontFamily.text-display-1-family'),
    "fontWeight": theme('fontWeight.text-display-1-moderate-weight'),
    "fontSize": theme('fontSize.text-display-1-size'),
    "lineHeight": theme('lineHeight.text-display-1-line-height')
  },
  ".text-display-1-strong": {
    "fontFamily": theme('fontFamily.text-display-1-family'),
    "fontSize": theme('fontSize.text-display-1-size'),
    "fontWeight": theme('fontWeight.text-display-1-strong-weight'),
    "lineHeight": theme('lineHeight.text-display-1-line-height')
  },
  ".text-heading-2-strong": {
    "fontFamily": theme('fontFamily.text-heading-2-family'),
    "fontWeight": theme('fontWeight.text-heading-2-strong-weight'),
    "fontSize": theme('fontSize.text-heading-2-size'),
    "lineHeight": theme('lineHeight.text-heading-2-line-height')
  },
  ".text-heading-2-moderate": {
    "fontFamily": theme('fontFamily.text-heading-2-family'),
    "fontSize": theme('fontSize.text-heading-2-size'),
    "fontWeight": theme('fontWeight.text-heading-2-moderate-weight'),
    "lineHeight": theme('lineHeight.text-heading-2-line-height')
  },
  ".text-heading-1-strong": {
    "fontFamily": theme('fontFamily.text-heading-1-family'),
    "fontWeight": theme('fontWeight.text-heading-1-strong-weight'),
    "fontSize": theme('fontSize.text-heading-1-size'),
    "lineHeight": theme('lineHeight.text-heading-1-line-height')
  },
  ".text-heading-1-moderate": {
    "fontFamily": theme('fontFamily.text-heading-1-family'),
    "fontWeight": theme('fontWeight.text-heading-1-moderate-weight'),
    "fontSize": theme('fontSize.text-heading-1-size'),
    "lineHeight": theme('lineHeight.text-heading-1-line-height')
  },
  ".text-heading-3-strong": {
    "fontFamily": theme('fontFamily.text-heading-3-family'),
    "fontWeight": theme('fontWeight.text-heading-3-strong-weight'),
    "fontSize": theme('fontSize.text-heading-3-size'),
    "lineHeight": theme('lineHeight.text-heading-3-line-height')
  },
  ".text-heading-3-moderate": {
    "fontFamily": theme('fontFamily.text-heading-3-family'),
    "fontWeight": theme('fontWeight.text-heading-3-moderate-weight'),
    "fontSize": theme('fontSize.text-heading-3-size'),
    "lineHeight": theme('lineHeight.text-heading-3-line-height')
  },
  ".text-body-1-strong": {
    "fontFamily": theme('fontFamily.text-body-1-family'),
    "fontWeight": theme('fontWeight.text-body-1-strong-weight'),
    "fontSize": theme('fontSize.text-body-1-size'),
    "lineHeight": theme('lineHeight.text-body-1-line-height')
  },
  ".text-body-1-moderate": {
    "fontFamily": theme('fontFamily.text-body-1-family'),
    "fontSize": theme('fontSize.text-body-1-size'),
    "fontWeight": theme('fontWeight.text-body-1-moderate-weight'),
    "lineHeight": theme('lineHeight.text-body-1-line-height')
  },
  ".text-body-3-strong": {
    "fontFamily": theme('fontFamily.text-body-3-family'),
    "fontWeight": theme('fontWeight.text-body-3-strong-weight'),
    "fontSize": theme('fontSize.text-body-3-size'),
    "lineHeight": theme('lineHeight.text-body-3-line-height')
  },
  ".text-body-3-moderate": {
    "fontFamily": theme('fontFamily.text-body-3-family'),
    "fontWeight": theme('fontWeight.text-body-3-moderate-weight'),
    "fontSize": theme('fontSize.text-body-3-size'),
    "lineHeight": theme('lineHeight.text-body-3-line-height')
  },
  ".text-body-2-strong": {
    "fontFamily": theme('fontFamily.text-body-2-family'),
    "fontWeight": theme('fontWeight.text-body-2-strong-weight'),
    "fontSize": theme('fontSize.text-body-2-size'),
    "lineHeight": theme('lineHeight.text-body-2-line-height')
  },
  ".text-body-2-moderate": {
    "fontFamily": theme('fontFamily.text-body-2-family'),
    "fontSize": theme('fontSize.text-body-2-size'),
    "fontWeight": theme('fontWeight.text-body-2-moderate-weight'),
    "lineHeight": theme('lineHeight.text-body-2-line-height')
  },
  ".text-caption-1-moderate": {
    "fontFamily": theme('fontFamily.text-caption-1-family'),
    "fontSize": theme('fontSize.text-caption-1-size'),
    "fontWeight": theme('fontWeight.text-caption-1-moderate-weight'),
    "lineHeight": theme('lineHeight.text-caption-1-line-height')
  }
})};