# @fincloud/lib-ui-tokens

This project integrates closely with Figma, Token Studio and Nexus to streamline the design token process for TailwindCSS.

## Overview

Designers use Token Studio to push Figma variables as tokens in JSON format, directly from Figma. Once these tokens are committed, a CI/CD pipeline automatically triggers the following scripts:

- **build:themes**: Executes `prepare-tw-themes.js` which transforms the tokens with the help of `StyleDictionary` library into multiple TailwindCSS theme configuration files.
- **build:components**: Executes `prepare-tw-components.js` which transforms the tokens with the help of `StyleDictionary` library into a TailwindCSS configuration file dedicated to custom components.

These transformations ensure that your TailwindCSS setup always reflects the latest design specifications.

## Workflow

1. **Design Token Submission**

   - The design team updates design tokens as a JSON file via Token Studio.
   - Once committed, the CI/CD pipeline is triggered.

2. **Token Transformation**

   - **Themes:** The script `prepare-tw-themes.js` processes the JSON tokens and generates configuration files for various TailwindCSS themes.
   - **Components:** The script `prepare-tw-components.js` processes the JSON tokens and generates a configuration file for custom TailwindCSS components.

3. **Package Generation and Publishing**

   - During the GitLab pipeline, an npm package is generated.
   - This package is then published to Nexus, making it available for consumption within your projects.

4. **TailwindCSS Integration**
   - The generated themes' JSON files are then used to update the TailwindCSS plugin `tailwindcss-themer` configuration, ensuring that the design tokens are directly integrated into your project’s styles.
   - The generated components JSON file is then used to update a TailwindCSS custom plugin configuration, ensuring that the design tokens are directly integrated as components into your project’s styles.

## Installation

Install the package with:

```bash
npm i @fincloud/lib-ui-tokens@latest
```
