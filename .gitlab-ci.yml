## CI Templates ##
include:
  - project: fincloud/cfg-ci-cd
    ref: master
    file:
      - /.ci-npm-publish.yml

image: node:18

before_script:
  - git config --global user.email "<EMAIL>"
  - git config --global user.name "CICD"
  - echo -e "machine git.neo.loan\nlogin gitlab-ci-token\npassword ${PUSH_TOKEN}" > ~/.netrc
  - chmod 600 ~/.netrc

bump_version:
  stage: prepare
  script:
    - npm i
    - npm version patch
    - npm run build:themes
    - npm run build:components
    - git add .
    - git commit -m  "update Tailwind config"
    - git remote add project-origin https://oauth2:${GITLAB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}
    - git push project-origin HEAD:master -o ci.skip
  artifacts:
    paths:
      - package.json
      - props/*

create_merge_request:
  extends: .create_merge_request

npm_publish:
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
